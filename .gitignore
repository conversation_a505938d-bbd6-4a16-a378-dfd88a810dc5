# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
share/python-wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# OS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*
.pnpm-debug.log*

# Build outputs
dist/
build/
*.tgz
*.tar.gz

# Environment variables
.env.local
.env.development.local
.env.test.local
.env.production.local

# Logs
logs/
*.log

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# Temporary files
tmp/
temp/
*.tmp

# Video files (for development)
*.mp4
*.avi
*.mov
*.mkv

# Audio files
*.wav
*.mp3
*.flac

# Image files (except templates)
*.jpg
*.jpeg
*.png
*.gif
*.bmp
*.tiff
!assets/templates/*.png
!assets/audio/*.wav

# Docker
.dockerignore

# Redis dump
dump.rdb

# S3/GCS credentials
credentials.json
*.pem
*.key

# Test outputs
test_outputs/
clips/
processed/
