"""Audio event detection using Mel spectrograms and cosine similarity."""

import os
import numpy as np
import librosa
import soundfile as sf
from typing import List, Dict, Tuple, Optional
from sklearn.metrics.pairwise import cosine_similarity
import structlog

from shared.models import DetectedEvent, EventType, ProcessingConfig

logger = structlog.get_logger()


class AudioEventDetector:
    """Detects game events from audio using reference samples and spectral analysis."""
    
    def __init__(self, assets_path: str = "assets", config: Optional[ProcessingConfig] = None):
        self.assets_path = assets_path
        self.config = config or ProcessingConfig()
        self.reference_specs = {}
        self.sample_rate = 22050  # Standard sample rate for analysis
        self.n_mels = 128  # Number of Mel frequency bins
        self.hop_length = 512
        self.n_fft = 2048
        
        # Load reference audio samples
        self._load_reference_samples()
    
    def _load_reference_samples(self):
        """Load and preprocess reference audio samples."""
        audio_dir = os.path.join(self.assets_path, "audio")
        
        # Expected reference files for each event type
        reference_files = {
            # Multi-kill events (multiple kills in quick succession)
            EventType.FIRST_BLOOD: "first_blood.wav",
            EventType.DOUBLE_KILL: "double_kill.wav",
            EventType.TRIPLE_KILL: "triple_kill.wav",
            EventType.MEGA_KILL: "mega_kill.wav",
            EventType.MANIAC: "maniac.wav",
            EventType.SAVAGE: "savage.wav",

            # Kill spree events (kill streak over time)
            EventType.KILLING_SPREE: "killing_spree.wav",
            EventType.MONSTER_KILL: "monster_kill.wav",
            EventType.GODLIKE: "godlike.wav",
            EventType.LEGENDARY: "legendary.wav",
            EventType.UNSTOPPABLE: "unstoppable.wav",

            # General kill event
            EventType.HAS_SLAIN: "has_slain.wav",
        }
        
        for event_type, filename in reference_files.items():
            file_path = os.path.join(audio_dir, filename)
            if os.path.exists(file_path):
                try:
                    # Load audio
                    audio, sr = librosa.load(file_path, sr=self.sample_rate)
                    
                    # Generate Mel spectrogram
                    mel_spec = librosa.feature.melspectrogram(
                        y=audio,
                        sr=self.sample_rate,
                        n_mels=self.n_mels,
                        hop_length=self.hop_length,
                        n_fft=self.n_fft
                    )
                    
                    # Convert to log scale
                    log_mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
                    
                    # Flatten for similarity comparison
                    self.reference_specs[event_type] = log_mel_spec.flatten()
                    
                    logger.info(
                        "Loaded reference audio",
                        event_type=event_type.value,
                        file_path=file_path,
                        duration=len(audio) / self.sample_rate,
                        spec_shape=log_mel_spec.shape
                    )
                    
                except Exception as e:
                    logger.error(
                        "Failed to load reference audio",
                        event_type=event_type.value,
                        file_path=file_path,
                        error=str(e)
                    )
            else:
                logger.warning(
                    "Reference audio file not found",
                    event_type=event_type.value,
                    file_path=file_path
                )
        
        logger.info(
            "Loaded reference audio samples",
            count=len(self.reference_specs),
            event_types=[et.value for et in self.reference_specs.keys()]
        )
    
    def extract_audio_from_video(self, video_path: str, output_path: str) -> bool:
        """Extract audio from video using ffmpeg."""
        try:
            import subprocess
            import shutil

            # Find ffmpeg executable
            ffmpeg_cmd = self._find_ffmpeg()
            if not ffmpeg_cmd:
                logger.error("FFmpeg not found in PATH")
                return False

            # Use ffmpeg to extract audio
            cmd = [
                ffmpeg_cmd, "-i", video_path,
                "-vn",  # No video
                "-acodec", "pcm_s16le",  # PCM 16-bit
                "-ar", str(self.sample_rate),  # Sample rate
                "-ac", "1",  # Mono
                "-y",  # Overwrite output
                output_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                logger.info(
                    "Extracted audio from video",
                    video_path=video_path,
                    output_path=output_path
                )
                return True
            else:
                logger.error(
                    "Failed to extract audio",
                    video_path=video_path,
                    error=result.stderr
                )
                return False
                
        except Exception as e:
            logger.error(
                "Audio extraction failed",
                video_path=video_path,
                error=str(e)
            )
            return False
    
    def detect_events(self, audio_path: str) -> List[DetectedEvent]:
        """Detect events in audio file using sliding window analysis."""
        if not self.reference_specs:
            logger.warning("No reference audio samples loaded")
            return []
        
        try:
            # Load audio file
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)
            audio_duration = len(audio) / self.sample_rate
            
            logger.info(
                "Analyzing audio for events",
                audio_path=audio_path,
                duration=audio_duration,
                sample_rate=sr
            )
            
            detected_events = []
            
            # Sliding window parameters
            window_size = int(self.config.audio_window_size * self.sample_rate)
            hop_size = int(self.config.audio_hop_size * self.sample_rate)
            
            # Slide window over audio
            for start_sample in range(0, len(audio) - window_size, hop_size):
                end_sample = start_sample + window_size
                window_audio = audio[start_sample:end_sample]
                
                # Calculate timestamp
                timestamp = start_sample / self.sample_rate

                # Check audio energy - skip if too quiet (likely silence or background noise)
                audio_energy = np.mean(window_audio ** 2)
                if audio_energy < 0.001:  # Very low energy threshold
                    continue

                # Generate Mel spectrogram for window
                mel_spec = librosa.feature.melspectrogram(
                    y=window_audio,
                    sr=self.sample_rate,
                    n_mels=self.n_mels,
                    hop_length=self.hop_length,
                    n_fft=self.n_fft
                )
                
                log_mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
                window_features = log_mel_spec.flatten()
                
                # Compare with each reference
                for event_type, ref_features in self.reference_specs.items():
                    # Ensure same length for comparison
                    min_len = min(len(window_features), len(ref_features))
                    window_feat_norm = window_features[:min_len]
                    ref_feat_norm = ref_features[:min_len]
                    
                    # Calculate cosine similarity
                    similarity = cosine_similarity(
                        [window_feat_norm], 
                        [ref_feat_norm]
                    )[0][0]
                    
                    # Check if similarity exceeds threshold with additional validation
                    if similarity > self.config.audio_similarity_threshold:
                        # Additional validation: check if this is a reasonable detection
                        # Avoid detecting the same event too frequently
                        is_valid_detection = True

                        # Check if we detected this event type recently (within 10 seconds)
                        recent_threshold = 10.0
                        for prev_event in detected_events[-20:]:  # Check last 20 events
                            if (prev_event.event_type == event_type and
                                abs(prev_event.timestamp - timestamp) < recent_threshold):
                                # Only keep if this one has significantly higher confidence
                                if similarity <= prev_event.confidence + 0.005:  # Must be 0.5% better
                                    is_valid_detection = False
                                    break

                        # Additional validation: check for minimum confidence gap
                        if is_valid_detection and similarity < 0.985:  # Even stricter minimum
                            is_valid_detection = False

                        # Additional validation: check audio energy is sufficient
                        if is_valid_detection and audio_energy < 0.01:  # Higher energy requirement
                            is_valid_detection = False

                        if is_valid_detection:
                            detected_events.append(DetectedEvent(
                                event_type=event_type,
                                timestamp=timestamp,
                                confidence=float(similarity),
                                source="audio",
                                metadata={
                                    "window_start": timestamp,
                                    "window_duration": self.config.audio_window_size,
                                    "similarity_score": similarity
                                }
                            ))
                        
                        logger.info(
                            "Detected audio event",
                            event_type=event_type.value,
                            timestamp=timestamp,
                            confidence=similarity
                        )
            
            # Remove duplicate detections (same event type within merge window)
            detected_events = self._merge_nearby_events(detected_events)
            
            logger.info(
                "Audio event detection complete",
                audio_path=audio_path,
                total_events=len(detected_events),
                event_types=[e.event_type.value for e in detected_events]
            )
            
            return detected_events
            
        except Exception as e:
            logger.error(
                "Audio event detection failed",
                audio_path=audio_path,
                error=str(e)
            )
            return []
    
    def _merge_nearby_events(self, events: List[DetectedEvent]) -> List[DetectedEvent]:
        """Merge events of the same type that are close in time."""
        if not events:
            return events
        
        # Sort by timestamp
        events.sort(key=lambda e: e.timestamp)
        
        merged_events = []
        current_event = events[0]
        
        for next_event in events[1:]:
            # Check if same event type and within merge window
            if (next_event.event_type == current_event.event_type and
                next_event.timestamp - current_event.timestamp <= self.config.merge_window):
                
                # Keep the one with higher confidence
                if next_event.confidence > current_event.confidence:
                    current_event = next_event
            else:
                # Different event or outside merge window
                merged_events.append(current_event)
                current_event = next_event
        
        # Add the last event
        merged_events.append(current_event)
        
        logger.info(
            "Merged nearby events",
            original_count=len(events),
            merged_count=len(merged_events)
        )
        
        return merged_events
    
    def create_reference_sample(self, audio_path: str, event_type: EventType, output_path: str):
        """Create a reference sample from an audio file (utility function)."""
        try:
            # Load and process audio
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            # Save as reference
            sf.write(output_path, audio, self.sample_rate)
            
            logger.info(
                "Created reference sample",
                event_type=event_type.value,
                input_path=audio_path,
                output_path=output_path
            )
            
        except Exception as e:
            logger.error(
                "Failed to create reference sample",
                event_type=event_type.value,
                error=str(e)
            )

    def _find_ffmpeg(self) -> str:
        """Find FFmpeg executable in common locations."""
        import shutil
        import os

        # Try common names
        for name in ["ffmpeg", "ffmpeg.exe"]:
            # Check if in PATH
            if shutil.which(name):
                return name

        # Check common Windows installation paths
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
            # Winget installation path
            os.path.expanduser(r"~\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe")
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None
