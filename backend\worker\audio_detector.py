"""Audio event detection using Mel spectrograms and cosine similarity."""

import os
import numpy as np
import librosa
import soundfile as sf
from typing import List, Dict, Tuple, Optional
from sklearn.metrics.pairwise import cosine_similarity
import structlog

from shared.models import DetectedEvent, EventType, ProcessingConfig

logger = structlog.get_logger()


class AudioEventDetector:
    """Detects game events from audio using reference samples and spectral analysis."""
    
    def __init__(self, assets_path: str = "assets", config: Optional[ProcessingConfig] = None):
        self.assets_path = assets_path
        self.config = config or ProcessingConfig()
        self.reference_specs = {}
        self.sample_rate = 22050  # Standard sample rate for analysis
        self.n_mels = 128  # Number of Mel frequency bins
        self.hop_length = 512
        self.n_fft = 2048
        
        # Load reference audio samples
        self._load_reference_samples()
    
    def _load_reference_samples(self):
        """Load and preprocess reference audio samples."""
        audio_dir = os.path.join(self.assets_path, "audio")
        
        # Expected reference files for each event type
        reference_files = {
            # Multi-kill events (multiple kills in quick succession)
            EventType.FIRST_BLOOD: "first_blood.wav",
            EventType.DOUBLE_KILL: "double_kill.wav",
            EventType.TRIPLE_KILL: "triple_kill.wav",
            EventType.MEGA_KILL: "mega_kill.wav",
            EventType.MANIAC: "maniac.wav",
            EventType.SAVAGE: "savage.wav",

            # Kill spree events (kill streak over time)
            EventType.KILLING_SPREE: "killing_spree.wav",
            EventType.MONSTER_KILL: "monster_kill.wav",
            EventType.GODLIKE: "godlike.wav",
            EventType.LEGENDARY: "legendary.wav",
            EventType.UNSTOPPABLE: "unstoppable.wav",

            # General kill event
            EventType.HAS_SLAIN: "has_slain.wav",
        }
        
        for event_type, filename in reference_files.items():
            file_path = os.path.join(audio_dir, filename)
            if os.path.exists(file_path):
                try:
                    # Load audio
                    audio, sr = librosa.load(file_path, sr=self.sample_rate)
                    
                    # Generate Mel spectrogram
                    mel_spec = librosa.feature.melspectrogram(
                        y=audio,
                        sr=self.sample_rate,
                        n_mels=self.n_mels,
                        hop_length=self.hop_length,
                        n_fft=self.n_fft
                    )
                    
                    # Convert to log scale
                    log_mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
                    
                    # Flatten for similarity comparison
                    self.reference_specs[event_type] = log_mel_spec.flatten()
                    
                    logger.info(
                        "Loaded reference audio",
                        event_type=event_type.value,
                        file_path=file_path,
                        duration=len(audio) / self.sample_rate,
                        spec_shape=log_mel_spec.shape
                    )
                    
                except Exception as e:
                    logger.error(
                        "Failed to load reference audio",
                        event_type=event_type.value,
                        file_path=file_path,
                        error=str(e)
                    )
            else:
                logger.warning(
                    "Reference audio file not found",
                    event_type=event_type.value,
                    file_path=file_path
                )
        
        logger.info(
            "Loaded reference audio samples",
            count=len(self.reference_specs),
            event_types=[et.value for et in self.reference_specs.keys()]
        )
    
    def extract_audio_from_video(self, video_path: str, output_path: str) -> bool:
        """Extract audio from video using ffmpeg."""
        try:
            import subprocess
            import shutil

            # Find ffmpeg executable
            ffmpeg_cmd = self._find_ffmpeg()
            if not ffmpeg_cmd:
                logger.error("FFmpeg not found in PATH")
                return False

            # Use ffmpeg to extract audio with voice-optimized filtering
            cmd = [
                ffmpeg_cmd, "-i", video_path,
                "-vn",  # No video
                "-acodec", "pcm_s16le",  # PCM 16-bit
                "-ar", str(self.sample_rate),  # Sample rate
                "-ac", "1",  # Mono
                # Relaxed band-pass filter (200-4000 Hz for game announcer voice)
                "-af", "highpass=f=200,lowpass=f=4000,volume=1.5",  # More lenient filtering
                "-y",  # Overwrite output
                output_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                logger.info(
                    "Extracted audio from video",
                    video_path=video_path,
                    output_path=output_path
                )
                return True
            else:
                logger.error(
                    "Failed to extract audio",
                    video_path=video_path,
                    error=result.stderr
                )
                return False
                
        except Exception as e:
            logger.error(
                "Audio extraction failed",
                video_path=video_path,
                error=str(e)
            )
            return False
    
    def detect_events(self, audio_path: str) -> List[DetectedEvent]:
        """Detect events in audio file using sliding window analysis."""
        if not self.reference_specs:
            logger.warning("No reference audio samples loaded")
            return []
        
        try:
            # Load audio file
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)
            audio_duration = len(audio) / self.sample_rate
            
            logger.info(
                "Analyzing audio for events",
                audio_path=audio_path,
                duration=audio_duration,
                sample_rate=sr
            )
            
            detected_events = []
            
            # Sliding window parameters
            window_size = int(self.config.audio_window_size * self.sample_rate)
            hop_size = int(self.config.audio_hop_size * self.sample_rate)
            
            # Slide window over audio
            for start_sample in range(0, len(audio) - window_size, hop_size):
                end_sample = start_sample + window_size
                window_audio = audio[start_sample:end_sample]
                
                # Calculate timestamp
                timestamp = start_sample / self.sample_rate

                # Relaxed audio gating: RMS energy check (-35 dBFS threshold)
                rms_energy = np.sqrt(np.mean(window_audio ** 2))
                rms_db = 20 * np.log10(rms_energy + 1e-10)  # Convert to dB
                if rms_db < -35:  # More lenient threshold for game audio
                    continue

                # Relaxed Voice Activity Detection - more permissive for game audio
                if not self._is_voice_activity_relaxed(window_audio):
                    continue

                # Generate Mel spectrogram for window
                mel_spec = librosa.feature.melspectrogram(
                    y=window_audio,
                    sr=self.sample_rate,
                    n_mels=self.n_mels,
                    hop_length=self.hop_length,
                    n_fft=self.n_fft
                )
                
                log_mel_spec = librosa.power_to_db(mel_spec, ref=np.max)
                window_features = log_mel_spec.flatten()
                
                # Compare with each reference
                for event_type, ref_features in self.reference_specs.items():
                    # Ensure same length for comparison
                    min_len = min(len(window_features), len(ref_features))
                    window_feat_norm = window_features[:min_len]
                    ref_feat_norm = ref_features[:min_len]
                    
                    # Calculate cosine similarity
                    similarity = cosine_similarity(
                        [window_feat_norm], 
                        [ref_feat_norm]
                    )[0][0]
                    
                    # Check if similarity exceeds threshold with additional validation
                    if similarity > self.config.audio_similarity_threshold:
                        # Additional validation: check if this is a reasonable detection
                        # Avoid detecting the same event too frequently
                        is_valid_detection = True

                        # Check if we detected this event type recently (within 10 seconds)
                        recent_threshold = 10.0
                        for prev_event in detected_events[-20:]:  # Check last 20 events
                            if (prev_event.event_type == event_type and
                                abs(prev_event.timestamp - timestamp) < recent_threshold):
                                # Only keep if this one has significantly higher confidence
                                if similarity <= prev_event.confidence + 0.005:  # Must be 0.5% better
                                    is_valid_detection = False
                                    break

                        # Relaxed validation: lower minimum confidence
                        if is_valid_detection and similarity < 0.80:  # More reasonable minimum
                            is_valid_detection = False

                        # Relaxed validation: lower energy requirement
                        if is_valid_detection and rms_db < -30:  # Lower energy requirement
                            is_valid_detection = False

                        if is_valid_detection:
                            detected_events.append(DetectedEvent(
                                event_type=event_type,
                                timestamp=timestamp,
                                confidence=float(similarity),
                                source="audio",
                                metadata={
                                    "window_start": timestamp,
                                    "window_duration": self.config.audio_window_size,
                                    "similarity_score": similarity
                                }
                            ))
                        
                        logger.info(
                            "Detected audio event",
                            event_type=event_type.value,
                            timestamp=timestamp,
                            confidence=similarity
                        )
            
            # Remove duplicate detections (same event type within merge window)
            detected_events = self._merge_nearby_events(detected_events)
            
            logger.info(
                "Audio event detection complete",
                audio_path=audio_path,
                total_events=len(detected_events),
                event_types=[e.event_type.value for e in detected_events]
            )
            
            return detected_events
            
        except Exception as e:
            logger.error(
                "Audio event detection failed",
                audio_path=audio_path,
                error=str(e)
            )
            return []
    
    def _merge_nearby_events(self, events: List[DetectedEvent]) -> List[DetectedEvent]:
        """Merge events of the same type that are close in time."""
        if not events:
            return events
        
        # Sort by timestamp
        events.sort(key=lambda e: e.timestamp)
        
        merged_events = []
        current_event = events[0]
        
        for next_event in events[1:]:
            # Check if same event type and within merge window
            if (next_event.event_type == current_event.event_type and
                next_event.timestamp - current_event.timestamp <= self.config.merge_window):
                
                # Keep the one with higher confidence
                if next_event.confidence > current_event.confidence:
                    current_event = next_event
            else:
                # Different event or outside merge window
                merged_events.append(current_event)
                current_event = next_event
        
        # Add the last event
        merged_events.append(current_event)
        
        logger.info(
            "Merged nearby events",
            original_count=len(events),
            merged_count=len(merged_events)
        )
        
        return merged_events
    
    def create_reference_sample(self, audio_path: str, event_type: EventType, output_path: str):
        """Create a reference sample from an audio file (utility function)."""
        try:
            # Load and process audio
            audio, sr = librosa.load(audio_path, sr=self.sample_rate)
            
            # Save as reference
            sf.write(output_path, audio, self.sample_rate)
            
            logger.info(
                "Created reference sample",
                event_type=event_type.value,
                input_path=audio_path,
                output_path=output_path
            )
            
        except Exception as e:
            logger.error(
                "Failed to create reference sample",
                event_type=event_type.value,
                error=str(e)
            )

    def _find_ffmpeg(self) -> str:
        """Find FFmpeg executable in common locations."""
        import shutil
        import os

        # Try common names
        for name in ["ffmpeg", "ffmpeg.exe"]:
            # Check if in PATH
            if shutil.which(name):
                return name

        # Check common Windows installation paths
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
            # Winget installation path
            os.path.expanduser(r"~\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe")
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None

    def _is_voice_activity(self, audio_window: np.ndarray) -> bool:
        """Simple Voice Activity Detection using spectral features."""
        try:
            # Convert to 16-bit PCM for VAD
            audio_16bit = (audio_window * 32767).astype(np.int16)

            # Simple VAD based on spectral characteristics
            # Voice has energy concentrated in 300-3400 Hz range
            fft = np.fft.rfft(audio_16bit)
            freqs = np.fft.rfftfreq(len(audio_16bit), 1/self.sample_rate)

            # Calculate energy in voice band (300-3400 Hz)
            voice_mask = (freqs >= 300) & (freqs <= 3400)
            voice_energy = np.sum(np.abs(fft[voice_mask]) ** 2)

            # Calculate total energy
            total_energy = np.sum(np.abs(fft) ** 2)

            # Voice activity if >40% of energy is in voice band
            if total_energy > 0:
                voice_ratio = voice_energy / total_energy
                return voice_ratio > 0.4

            return False

        except Exception as e:
            logger.warning("VAD failed, assuming voice activity", error=str(e))
            return True  # Conservative fallback

    def _is_voice_activity_relaxed(self, audio_window: np.ndarray) -> bool:
        """Relaxed Voice Activity Detection for game audio."""
        try:
            # Much more permissive VAD for game announcer audio
            # Just check if there's sufficient energy in any frequency range
            fft = np.fft.rfft(audio_window)
            total_energy = np.sum(np.abs(fft) ** 2)

            # Very low threshold - just avoid complete silence
            if total_energy > 1e-6:
                return True

            return False

        except Exception as e:
            logger.warning("Relaxed VAD failed, assuming voice activity", error=str(e))
            return True  # Very conservative fallback

    def _generate_audio_fingerprint(self, audio_window: np.ndarray) -> set:
        """Generate Shazam-style audio fingerprint from audio window."""
        try:
            # Generate spectrogram with 43ms windows (professional standard)
            hop_length = int(0.043 * self.sample_rate)  # 43ms
            n_fft = 4096

            stft = librosa.stft(audio_window, n_fft=n_fft, hop_length=hop_length)
            magnitude = np.abs(stft)

            # Find spectral peaks (local maxima)
            from scipy.ndimage import maximum_filter

            # Apply local maximum filter
            neighborhood_size = 3
            local_maxima = maximum_filter(magnitude, size=neighborhood_size) == magnitude

            # Threshold for peak detection
            threshold = np.percentile(magnitude, 85)  # Top 15% of magnitudes
            peaks = local_maxima & (magnitude > threshold)

            # Get peak coordinates
            freq_indices, time_indices = np.where(peaks)

            # Generate fingerprint hashes
            fingerprints = set()

            # For each anchor peak, pair with nearby peaks
            for i, (f1, t1) in enumerate(zip(freq_indices, time_indices)):
                # Look for target peaks within 200ms window
                time_window = int(0.2 * self.sample_rate / hop_length)  # 200ms

                for j, (f2, t2) in enumerate(zip(freq_indices, time_indices)):
                    if i != j and t1 < t2 <= t1 + time_window:
                        # Create fingerprint hash: (f1, f2, dt)
                        dt = t2 - t1
                        if dt > 0 and dt < time_window:
                            # Create hash (simplified version)
                            hash_val = (f1 << 20) | (f2 << 10) | dt
                            fingerprints.add(hash_val)

            return fingerprints

        except Exception as e:
            logger.warning("Fingerprint generation failed", error=str(e))
            return set()

    def _match_fingerprints(self, query_fingerprints: set, reference_fingerprints: set, min_matches: int = 10) -> float:
        """Match fingerprints and return confidence score."""
        if not query_fingerprints or not reference_fingerprints:
            return 0.0

        # Count matching hashes
        matches = len(query_fingerprints & reference_fingerprints)

        # Calculate confidence based on match ratio
        total_query = len(query_fingerprints)
        total_ref = len(reference_fingerprints)

        if matches >= min_matches and total_query > 0:
            # Confidence based on match density
            confidence = matches / min(total_query, total_ref)
            return min(confidence, 1.0)

        return 0.0
