#!/usr/bin/env python3
"""
Debug Whisper transcription to see what it's actually hearing.
"""

import sys
import os
import tempfile

# Add backend to path
sys.path.append('backend')

def debug_whisper():
    """Debug what Whisper is actually transcribing."""
    print("🔍 Debugging Whisper Transcription")
    print("=" * 50)
    
    try:
        from faster_whisper import WhisperModel
        import librosa
        import soundfile as sf
        import numpy as np
        
        # Load Whisper model
        print("🎤 Loading Whisper model...")
        model = WhisperModel("tiny.en", compute_type="int8", device="cpu")
        print("✅ Whisper model loaded")
        
        # Test a few audio files
        audio_dir = "assets/audio"
        test_files = ["double_kill.wav", "first_blood.wav", "godlike.wav"]
        
        with tempfile.TemporaryDirectory() as temp_dir:
            for audio_file in test_files:
                audio_path = os.path.join(audio_dir, audio_file)
                
                if not os.path.exists(audio_path):
                    print(f"❌ File not found: {audio_path}")
                    continue
                
                print(f"\n🎵 Analyzing: {audio_file}")
                
                # Load and prepare audio
                audio_data, sr = librosa.load(audio_path, sr=None)
                print(f"   📊 Original: {len(audio_data)} samples at {sr}Hz")
                
                # Add silence and resample to 16kHz
                silence = np.zeros(int(1 * sr))
                test_audio = np.concatenate([silence, audio_data, silence])
                test_audio_16k = librosa.resample(test_audio, orig_sr=sr, target_sr=16000)
                
                test_path = os.path.join(temp_dir, f"test_{audio_file}")
                sf.write(test_path, test_audio_16k, 16000)
                
                print(f"   🔄 Processed: {len(test_audio_16k)} samples at 16kHz")
                
                # Test 1: Transcribe with VAD
                print(f"   🎤 Transcribing with VAD...")
                try:
                    segments, info = model.transcribe(
                        test_path,
                        vad_filter=True,
                        word_timestamps=True,
                        language="en",
                        condition_on_previous_text=False,
                        temperature=0.0
                    )
                    
                    print(f"   📊 Language: {info.language} (confidence: {info.language_probability:.3f})")
                    
                    segments_list = list(segments)
                    print(f"   📝 Segments with VAD: {len(segments_list)}")
                    
                    for i, segment in enumerate(segments_list):
                        print(f"      {i+1}. [{segment.start:.2f}s - {segment.end:.2f}s] '{segment.text.strip()}'")
                        if hasattr(segment, 'words') and segment.words:
                            for word in segment.words:
                                print(f"         Word: '{word.word}' at {word.start:.2f}s (prob: {word.probability:.3f})")
                
                except Exception as e:
                    print(f"   ❌ VAD transcription failed: {e}")
                
                # Test 2: Transcribe without VAD
                print(f"   🎤 Transcribing without VAD...")
                try:
                    segments, info = model.transcribe(
                        test_path,
                        vad_filter=False,  # Disable VAD
                        word_timestamps=True,
                        language="en",
                        condition_on_previous_text=False,
                        temperature=0.0,
                        no_speech_threshold=0.1  # Lower threshold
                    )
                    
                    segments_list = list(segments)
                    print(f"   📝 Segments without VAD: {len(segments_list)}")
                    
                    for i, segment in enumerate(segments_list):
                        print(f"      {i+1}. [{segment.start:.2f}s - {segment.end:.2f}s] '{segment.text.strip()}'")
                        if hasattr(segment, 'words') and segment.words:
                            for word in segment.words:
                                print(f"         Word: '{word.word}' at {word.start:.2f}s (prob: {word.probability:.3f})")
                
                except Exception as e:
                    print(f"   ❌ No-VAD transcription failed: {e}")
                
                # Test 3: Try with different language detection
                print(f"   🌍 Trying auto language detection...")
                try:
                    segments, info = model.transcribe(
                        test_path,
                        vad_filter=False,
                        word_timestamps=True,
                        language=None,  # Auto-detect
                        condition_on_previous_text=False,
                        temperature=0.0,
                        no_speech_threshold=0.1
                    )
                    
                    print(f"   📊 Detected language: {info.language} (confidence: {info.language_probability:.3f})")
                    
                    segments_list = list(segments)
                    print(f"   📝 Auto-language segments: {len(segments_list)}")
                    
                    for i, segment in enumerate(segments_list):
                        print(f"      {i+1}. [{segment.start:.2f}s - {segment.end:.2f}s] '{segment.text.strip()}'")
                
                except Exception as e:
                    print(f"   ❌ Auto-language transcription failed: {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    success = debug_whisper()
    if success:
        print("\n🎉 Whisper debug completed!")
    else:
        print("\n❌ Whisper debug failed!")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
