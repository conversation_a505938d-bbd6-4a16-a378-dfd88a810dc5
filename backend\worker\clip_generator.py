"""Video clip generation and post-processing using FFmpeg."""

import os
import subprocess
import asyncio
from typing import Optional, <PERSON><PERSON>
import structlog

from shared.models import EventType, ProcessingConfig

logger = structlog.get_logger()


class ClipGenerator:
    """Generates video clips with FFmpeg processing."""
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        self.config = config or ProcessingConfig()
        
        # Event type display names for labels
        self.event_labels = {
            # Multi-kill events (multiple kills in quick succession)
            EventType.FIRST_BLOOD: "FIRST BLOOD",
            EventType.DOUBLE_KILL: "DOUBLE KILL",
            EventType.TRIPLE_KILL: "TRIPLE KILL",
            EventType.MEGA_KILL: "MEGA KILL",
            EventType.MANIAC: "MANIAC",
            EventType.SAVAGE: "SAVAGE",

            # Kill spree events (kill streak over time)
            EventType.KILLING_SPREE: "KILLING SPREE",
            EventType.MONSTER_KILL: "MONSTER KILL",
            EventType.GODLIKE: "GODLIKE",
            EventType.LEGENDARY: "LEGENDARY",
            EventType.UNSTOPPABLE: "UNSTOPPABLE",

            # General kill event
            EventType.HAS_SLAIN: "HAS SLAIN",
        }
    
    async def generate_clip(
        self,
        video_path: str,
        output_path: str,
        start_time: float,
        event_type: EventType,
        duration: Optional[float] = None,
        event_metadata: Optional[dict] = None
    ) -> bool:
        """Generate a video clip with 9:16 aspect ratio and burned-in label."""
        
        if duration is None:
            duration = self.config.clip_config.clip_duration
        
        # Calculate actual start time with padding
        # Check if this is a chained event with custom start time
        if event_metadata and event_metadata.get('clip_start_override'):
            actual_start = max(0, event_metadata['clip_start_override'])
            # Adjust duration for chained events to capture the full sequence
            sequence_duration = event_metadata.get('sequence_duration', 0)
            duration = max(duration, sequence_duration + self.config.clip_config.padding_after)

            logger.info(
                "Using chained event timing",
                event_type=event_type.value,
                original_start=start_time,
                chain_start=actual_start,
                sequence_duration=sequence_duration,
                final_duration=duration
            )
        else:
            actual_start = max(0, start_time - self.config.clip_config.padding_before)
        
        try:
            logger.info(
                "Generating clip",
                video_path=video_path,
                output_path=output_path,
                start_time=start_time,
                actual_start=actual_start,
                duration=duration,
                event_type=event_type.value
            )
            
            # Get video dimensions first
            video_info = await self._get_video_info(video_path)
            if not video_info:
                logger.error("Failed to get video info", video_path=video_path)
                return False
            
            width, height, fps = video_info
            
            # Calculate crop parameters for 9:16 aspect ratio
            crop_params = self._calculate_crop_params(width, height)
            
            # Generate the clip
            success = await self._extract_and_process_clip(
                video_path=video_path,
                output_path=output_path,
                start_time=actual_start,
                duration=duration,
                crop_params=crop_params,
                event_type=event_type,
                fps=fps,
                event_metadata=event_metadata
            )
            
            if success:
                logger.info(
                    "Clip generated successfully",
                    output_path=output_path,
                    event_type=event_type.value
                )
            else:
                logger.error(
                    "Clip generation failed",
                    output_path=output_path,
                    event_type=event_type.value
                )
            
            return success
            
        except Exception as e:
            logger.error(
                "Clip generation error",
                video_path=video_path,
                output_path=output_path,
                event_type=event_type.value,
                error=str(e)
            )
            return False
    
    async def _get_video_info(self, video_path: str) -> Optional[Tuple[int, int, float]]:
        """Get video dimensions and frame rate using ffprobe."""
        try:
            # Find ffprobe executable
            ffprobe_cmd = self._find_ffprobe()
            if not ffprobe_cmd:
                logger.error("FFprobe not found")
                return None

            cmd = [
                ffprobe_cmd,
                "-v", "quiet",
                "-print_format", "json",
                "-show_streams",
                "-select_streams", "v:0",
                video_path
            ]
            
            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                import json
                data = json.loads(stdout.decode())
                
                if data.get("streams"):
                    stream = data["streams"][0]
                    width = int(stream.get("width", 0))
                    height = int(stream.get("height", 0))
                    
                    # Parse frame rate
                    fps_str = stream.get("r_frame_rate", "30/1")
                    if "/" in fps_str:
                        num, den = fps_str.split("/")
                        fps = float(num) / float(den)
                    else:
                        fps = float(fps_str)
                    
                    return width, height, fps
            
            logger.error("ffprobe failed", video_path=video_path, stderr=stderr.decode())
            return None
            
        except Exception as e:
            logger.error("Failed to get video info", video_path=video_path, error=str(e))
            return None
    
    def _calculate_crop_params(self, width: int, height: int) -> dict:
        """Calculate crop parameters for 9:16 aspect ratio."""
        target_ratio = 9 / 16
        current_ratio = width / height
        
        if current_ratio > target_ratio:
            # Video is wider, crop width
            new_width = int(height * target_ratio)
            new_height = height
            x_offset = (width - new_width) // 2
            y_offset = 0
        else:
            # Video is taller, crop height
            new_width = width
            new_height = int(width / target_ratio)
            x_offset = 0
            y_offset = (height - new_height) // 2
        
        return {
            "width": new_width,
            "height": new_height,
            "x": x_offset,
            "y": y_offset
        }
    
    async def _extract_and_process_clip(
        self,
        video_path: str,
        output_path: str,
        start_time: float,
        duration: float,
        crop_params: dict,
        event_type: EventType,
        fps: float,
        event_metadata: Optional[dict] = None
    ) -> bool:
        """Extract clip and apply processing using FFmpeg."""
        try:
            # Get event label - enhance for chained events
            label_text = self.event_labels.get(event_type, event_type.value.upper())

            # Add chain information to label if this is a chained event
            if event_metadata and event_metadata.get('is_chained'):
                chain_length = event_metadata.get('chain_length', 1)
                if chain_length > 1:
                    label_text = f"{label_text} ({chain_length}x CHAIN)"
            
            # Find ffmpeg executable
            ffmpeg_cmd = self._find_ffmpeg()
            if not ffmpeg_cmd:
                logger.error("FFmpeg not found")
                return False

            # Build ultra-fast FFmpeg command using stream copy (no re-encoding)
            cmd = [
                ffmpeg_cmd,
                "-ss", str(start_time),  # Start time (seek before input for speed)
                "-i", video_path,        # Input file
                "-t", str(duration),     # Duration
                "-c", "copy",            # Stream copy - NO RE-ENCODING (100x faster!)
                "-avoid_negative_ts", "make_zero",  # Fix timestamp issues
                "-movflags", "+faststart",  # Web optimization
                "-y",                    # Overwrite output
            ]
            
            # Add output file (no video filters for maximum speed with stream copy)
            cmd.append(output_path)

            # Note: Stream copy mode sacrifices video processing (crop, scale, text) for speed
            # This extracts clips 100x faster but without visual enhancements
            
            logger.info(
                "Running FFmpeg command",
                cmd=" ".join(cmd),
                output_path=output_path
            )
            
            # Run FFmpeg
            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                # Verify output file exists and has reasonable size
                if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                    return True
                else:
                    logger.error("Output file missing or too small", output_path=output_path)
                    return False
            else:
                logger.error(
                    "FFmpeg failed",
                    output_path=output_path,
                    returncode=result.returncode,
                    stderr=stderr.decode()
                )
                return False
                
        except Exception as e:
            logger.error(
                "FFmpeg processing failed",
                output_path=output_path,
                error=str(e)
            )
            return False

    def _find_ffmpeg(self) -> str:
        """Find FFmpeg executable in common locations."""
        import shutil
        import os

        # Try common names
        for name in ["ffmpeg", "ffmpeg.exe"]:
            # Check if in PATH
            if shutil.which(name):
                return name

        # Check common Windows installation paths
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
            # Winget installation path
            os.path.expanduser(r"~\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe")
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None

    def _find_ffprobe(self) -> str:
        """Find FFprobe executable in common locations."""
        import shutil
        import os

        # Try common names
        for name in ["ffprobe", "ffprobe.exe"]:
            # Check if in PATH
            if shutil.which(name):
                return name

        # Check common Windows installation paths
        common_paths = [
            r"C:\ffmpeg\bin\ffprobe.exe",
            r"C:\Program Files\ffmpeg\bin\ffprobe.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffprobe.exe",
            # Winget installation path
            os.path.expanduser(r"~\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffprobe.exe")
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None
