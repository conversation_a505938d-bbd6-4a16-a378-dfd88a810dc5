"""Video clip generation and post-processing using FFmpeg."""

import os
import subprocess
import asyncio
from typing import Optional, <PERSON><PERSON>
import structlog

from ..shared.models import EventType, ProcessingConfig

logger = structlog.get_logger()


class ClipGenerator:
    """Generates video clips with FFmpeg processing."""
    
    def __init__(self, config: Optional[ProcessingConfig] = None):
        self.config = config or ProcessingConfig()
        
        # Event type display names for labels
        self.event_labels = {
            EventType.DOUBLE_KILL: "DOUBLE KILL",
            EventType.TRIPLE_KILL: "TRIPLE KILL",
            EventType.MANIAC: "MANIAC",
            EventType.SAVAGE: "SAVAGE",
            EventType.FIRST_BLOOD: "FIRST BLOOD",
            EventType.KILL_STREAK: "KILLING SPREE",
            EventType.LORD_KILL: "LORD KILL",
            EventType.TURTLE_KILL: "TURTLE KILL",
            EventType.CRYSTAL_DESTROY: "CRYSTAL DESTROYED",
            EventType.TOWER_DESTROY: "TOWER DESTROYED",
        }
    
    async def generate_clip(
        self,
        video_path: str,
        output_path: str,
        start_time: float,
        event_type: EventType,
        duration: Optional[float] = None
    ) -> bool:
        """Generate a video clip with 9:16 aspect ratio and burned-in label."""
        
        if duration is None:
            duration = self.config.clip_config.clip_duration
        
        # Calculate actual start time with padding
        actual_start = max(0, start_time - self.config.clip_config.padding_before)
        
        try:
            logger.info(
                "Generating clip",
                video_path=video_path,
                output_path=output_path,
                start_time=start_time,
                actual_start=actual_start,
                duration=duration,
                event_type=event_type.value
            )
            
            # Get video dimensions first
            video_info = await self._get_video_info(video_path)
            if not video_info:
                logger.error("Failed to get video info", video_path=video_path)
                return False
            
            width, height, fps = video_info
            
            # Calculate crop parameters for 9:16 aspect ratio
            crop_params = self._calculate_crop_params(width, height)
            
            # Generate the clip
            success = await self._extract_and_process_clip(
                video_path=video_path,
                output_path=output_path,
                start_time=actual_start,
                duration=duration,
                crop_params=crop_params,
                event_type=event_type,
                fps=fps
            )
            
            if success:
                logger.info(
                    "Clip generated successfully",
                    output_path=output_path,
                    event_type=event_type.value
                )
            else:
                logger.error(
                    "Clip generation failed",
                    output_path=output_path,
                    event_type=event_type.value
                )
            
            return success
            
        except Exception as e:
            logger.error(
                "Clip generation error",
                video_path=video_path,
                output_path=output_path,
                event_type=event_type.value,
                error=str(e)
            )
            return False
    
    async def _get_video_info(self, video_path: str) -> Optional[Tuple[int, int, float]]:
        """Get video dimensions and frame rate using ffprobe."""
        try:
            cmd = [
                "ffprobe",
                "-v", "quiet",
                "-print_format", "json",
                "-show_streams",
                "-select_streams", "v:0",
                video_path
            ]
            
            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                import json
                data = json.loads(stdout.decode())
                
                if data.get("streams"):
                    stream = data["streams"][0]
                    width = int(stream.get("width", 0))
                    height = int(stream.get("height", 0))
                    
                    # Parse frame rate
                    fps_str = stream.get("r_frame_rate", "30/1")
                    if "/" in fps_str:
                        num, den = fps_str.split("/")
                        fps = float(num) / float(den)
                    else:
                        fps = float(fps_str)
                    
                    return width, height, fps
            
            logger.error("ffprobe failed", video_path=video_path, stderr=stderr.decode())
            return None
            
        except Exception as e:
            logger.error("Failed to get video info", video_path=video_path, error=str(e))
            return None
    
    def _calculate_crop_params(self, width: int, height: int) -> dict:
        """Calculate crop parameters for 9:16 aspect ratio."""
        target_ratio = 9 / 16
        current_ratio = width / height
        
        if current_ratio > target_ratio:
            # Video is wider, crop width
            new_width = int(height * target_ratio)
            new_height = height
            x_offset = (width - new_width) // 2
            y_offset = 0
        else:
            # Video is taller, crop height
            new_width = width
            new_height = int(width / target_ratio)
            x_offset = 0
            y_offset = (height - new_height) // 2
        
        return {
            "width": new_width,
            "height": new_height,
            "x": x_offset,
            "y": y_offset
        }
    
    async def _extract_and_process_clip(
        self,
        video_path: str,
        output_path: str,
        start_time: float,
        duration: float,
        crop_params: dict,
        event_type: EventType,
        fps: float
    ) -> bool:
        """Extract clip and apply processing using FFmpeg."""
        try:
            # Get event label
            label_text = self.event_labels.get(event_type, event_type.value.upper())
            
            # Build FFmpeg command
            cmd = [
                "ffmpeg",
                "-ss", str(start_time),  # Start time
                "-i", video_path,        # Input file
                "-t", str(duration),     # Duration
                "-c:v", self.config.clip_config.video_codec,  # Video codec
                "-c:a", self.config.clip_config.audio_codec,  # Audio codec
                "-crf", str(self.config.clip_config.crf),     # Quality
                "-preset", "fast",       # Encoding speed
                "-movflags", "+faststart",  # Web optimization
            ]
            
            # Add video filters
            filters = []
            
            # Crop to 9:16
            crop_filter = f"crop={crop_params['width']}:{crop_params['height']}:{crop_params['x']}:{crop_params['y']}"
            filters.append(crop_filter)
            
            # Scale to standard TikTok size (1080x1920)
            scale_filter = "scale=1080:1920"
            filters.append(scale_filter)
            
            # Add text overlay (label)
            text_filter = (
                f"drawtext=text='{label_text}'"
                f":fontsize=60"
                f":fontcolor=white"
                f":bordercolor=black"
                f":borderw=3"
                f":x=(w-text_w)/2"
                f":y=h-text_h-50"
                f":enable='between(t,0.5,{duration-0.5})'"  # Show for most of the clip
            )
            filters.append(text_filter)
            
            # Combine filters
            filter_complex = ",".join(filters)
            cmd.extend(["-vf", filter_complex])
            
            # Output settings
            cmd.extend([
                "-y",  # Overwrite output
                output_path
            ])
            
            logger.info(
                "Running FFmpeg command",
                cmd=" ".join(cmd),
                output_path=output_path
            )
            
            # Run FFmpeg
            result = await asyncio.create_subprocess_exec(
                *cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            stdout, stderr = await result.communicate()
            
            if result.returncode == 0:
                # Verify output file exists and has reasonable size
                if os.path.exists(output_path) and os.path.getsize(output_path) > 1000:
                    return True
                else:
                    logger.error("Output file missing or too small", output_path=output_path)
                    return False
            else:
                logger.error(
                    "FFmpeg failed",
                    output_path=output_path,
                    returncode=result.returncode,
                    stderr=stderr.decode()
                )
                return False
                
        except Exception as e:
            logger.error(
                "FFmpeg processing failed",
                output_path=output_path,
                error=str(e)
            )
            return False
