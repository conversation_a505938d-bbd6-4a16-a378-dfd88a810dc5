"""Main worker process for video processing."""

import asyncio
import os
import signal
import sys
from typing import Optional
import structlog

from shared.config import get_settings
from gateway.queue import get_queue_client
from worker.video_processor import VideoProcessor

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


class Worker:
    """Video processing worker."""
    
    def __init__(self):
        self.settings = get_settings()
        self.running = False
        # Fix assets path when running from backend directory
        assets_path = self.settings.assets_path
        if not os.path.exists(assets_path):
            # Try parent directory
            parent_assets = os.path.join("..", assets_path)
            if os.path.exists(parent_assets):
                assets_path = parent_assets

        self.processor = VideoProcessor(
            assets_path=assets_path,
            temp_path=self.settings.temp_path
        )
        self.queue_client = None
    
    async def start(self):
        """Start the worker process."""
        logger.info("Starting ML Highlights Worker", version="1.0.0")
        
        # Initialize queue client
        self.queue_client = await get_queue_client()
        
        # Test connections
        if not await self.queue_client.ping():
            logger.error("Failed to connect to Redis")
            return
        
        logger.info("Worker initialized successfully")
        
        # Set up signal handlers
        self._setup_signal_handlers()
        
        # Start processing loop
        self.running = True
        await self._process_loop()
    
    def _setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            logger.info("Received shutdown signal", signal=signum)
            self.running = False
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    async def _process_loop(self):
        """Main processing loop."""
        logger.info("Worker processing loop started")
        
        while self.running:
            try:
                # Dequeue job (blocking with timeout)
                job_data = await self.queue_client.dequeue_job(timeout=10)
                
                if job_data:
                    await self._process_job(job_data)
                else:
                    # No job available, continue loop
                    continue
                    
            except Exception as e:
                logger.error("Error in processing loop", error=str(e))
                # Brief pause before retrying
                await asyncio.sleep(5)
        
        logger.info("Worker processing loop stopped")
    
    async def _process_job(self, job_data: dict):
        """Process a single job."""
        job_id = job_data.get("job_id")
        s3_key = job_data.get("s3_key")
        
        if not job_id or not s3_key:
            logger.error("Invalid job data", job_data=job_data)
            return
        
        logger.info("Processing job", job_id=job_id, s3_key=s3_key)
        
        try:
            # Get job from Redis
            job = await self.queue_client.get_job(job_id)
            if not job:
                logger.error("Job not found in Redis", job_id=job_id)
                return
            
            # Check if file exists in S3
            if not await self.processor.storage_client.file_exists(s3_key):
                logger.error("Video file not found in S3", job_id=job_id, s3_key=s3_key)
                job.status = "failed"
                job.error_message = "Video file not found in storage"
                await self.queue_client.update_job(job)
                return
            
            # Process the video
            processed_job = await self.processor.process_video(job, s3_key)
            
            # Update job in Redis
            await self.queue_client.update_job(processed_job)
            
            logger.info(
                "Job processing completed",
                job_id=job_id,
                status=processed_job.status.value,
                clips_generated=len(processed_job.generated_clips),
                processing_time=processed_job.processing_time
            )
            
        except Exception as e:
            logger.error("Job processing failed", job_id=job_id, error=str(e))
            
            # Update job status to failed
            try:
                job = await self.queue_client.get_job(job_id)
                if job:
                    job.status = "failed"
                    job.error_message = str(e)
                    await self.queue_client.update_job(job)
            except Exception as update_error:
                logger.error("Failed to update job status", job_id=job_id, error=str(update_error))
    
    async def stop(self):
        """Stop the worker gracefully."""
        logger.info("Stopping worker")
        self.running = False


async def main():
    """Main entry point."""
    worker = Worker()
    
    try:
        await worker.start()
    except KeyboardInterrupt:
        logger.info("Received keyboard interrupt")
    except Exception as e:
        logger.error("Worker failed", error=str(e))
        sys.exit(1)
    finally:
        await worker.stop()
        logger.info("Worker stopped")


if __name__ == "__main__":
    # Run the worker
    asyncio.run(main())
