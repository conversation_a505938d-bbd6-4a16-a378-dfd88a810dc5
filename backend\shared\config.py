"""Configuration management for the ML Highlights service."""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class Settings(BaseSettings):
    """Application settings loaded from environment variables."""

    model_config = {
        "extra": "ignore",  # Allow extra fields in .env file
        "env_file": ".env",
        "env_file_encoding": "utf-8"
    }

    # API Configuration
    api_host: str = Field(default="0.0.0.0", env="API_HOST")
    api_port: int = Field(default=8000, env="API_PORT")
    debug: bool = Field(default=False, env="DEBUG")
    log_level: str = Field(default="INFO", env="LOG_LEVEL")
    
    # Redis Configuration
    redis_url: str = Field(default="redis://localhost:6379", env="REDIS_URL")
    
    # S3-Compatible Storage Configuration (AWS S3 or Backblaze B2)
    s3_bucket: str = Field(..., env="S3_BUCKET")
    s3_access_key_id: str = Field(..., env="S3_ACCESS_KEY_ID")
    s3_secret_access_key: str = Field(..., env="S3_SECRET_ACCESS_KEY")
    s3_region: str = Field(default="us-east-1", env="S3_REGION")
    s3_endpoint_url: Optional[str] = Field(default=None, env="S3_ENDPOINT_URL")

    # Storage provider type
    storage_provider: str = Field(default="aws", env="STORAGE_PROVIDER")  # "aws" or "backblaze"
    
    # File Upload Configuration
    max_file_size: int = Field(default=**********, env="MAX_FILE_SIZE")  # 2GB
    allowed_extensions: str = Field(default="mp4,avi,mov,mkv", env="ALLOWED_EXTENSIONS")
    upload_timeout: int = Field(default=3600, env="UPLOAD_TIMEOUT")  # 1 hour
    
    # Worker Configuration
    max_workers: int = Field(default=2, env="MAX_WORKERS")
    worker_timeout: int = Field(default=300, env="WORKER_TIMEOUT")  # 5 minutes
    
    # Processing Configuration
    audio_similarity_threshold: float = Field(default=0.83, env="AUDIO_SIMILARITY_THRESHOLD")
    visual_similarity_threshold: float = Field(default=0.90, env="VISUAL_SIMILARITY_THRESHOLD")
    clip_duration: float = Field(default=6.0, env="CLIP_DURATION")
    clip_padding: float = Field(default=2.0, env="CLIP_PADDING")
    
    # Paths
    assets_path: str = Field(default="assets", env="ASSETS_PATH")
    temp_path: str = Field(default=None, env="TEMP_PATH")
    

    
    @property
    def allowed_extensions_list(self) -> list[str]:
        """Get allowed file extensions as a list."""
        return [ext.strip().lower() for ext in self.allowed_extensions.split(",")]
    
    @property
    def s3_raw_prefix(self) -> str:
        """S3 prefix for raw uploaded videos."""
        return "raw/"
    
    @property
    def s3_clips_prefix(self) -> str:
        """S3 prefix for generated clips."""
        return "clips/"
    
    @property
    def s3_zips_prefix(self) -> str:
        """S3 prefix for zip files."""
        return "zips/"


# Global settings instance
settings = Settings()


def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings
