#!/usr/bin/env python3
"""
Complete setup validation for ML Highlights system.
Checks both audio files and visual templates.
"""

import os
import sys
from pathlib import Path
from enum import Enum

class EventType(str, Enum):
    """Types of game events that can be detected."""
    # Multi-kill events (audio) - multiple kills in quick succession
    FIRST_BLOOD = "first_blood"
    DOUBLE_KILL = "double_kill"
    TRIPLE_KILL = "triple_kill"
    MEGA_KILL = "mega_kill"
    MANIAC = "maniac"
    SAVAGE = "savage"
    
    # Kill spree events (audio) - kill streak over time
    KILLING_SPREE = "killing_spree"
    MONSTER_KILL = "monster_kill"
    GODLIKE = "godlike"
    LEGENDARY = "legendary"
    UNSTOPPABLE = "unstoppable"
    
    # General kill event (audio)
    HAS_SLAIN = "has_slain"

def check_audio_files():
    """Check if all audio files are present."""
    print("🎵 Checking Audio Files")
    print("=" * 50)
    
    audio_dir = Path("assets/audio")
    if not audio_dir.exists():
        print(f"❌ Audio directory not found: {audio_dir}")
        return False
    
    expected_files = {
        EventType.FIRST_BLOOD: "first_blood.wav",
        EventType.DOUBLE_KILL: "double_kill.wav", 
        EventType.TRIPLE_KILL: "triple_kill.wav",
        EventType.MEGA_KILL: "mega_kill.wav",
        EventType.MANIAC: "maniac.wav",
        EventType.SAVAGE: "savage.wav",
        EventType.KILLING_SPREE: "killing_spree.wav",
        EventType.MONSTER_KILL: "monster_kill.wav",
        EventType.GODLIKE: "godlike.wav",
        EventType.LEGENDARY: "legendary.wav",
        EventType.UNSTOPPABLE: "unstoppable.wav",
        EventType.HAS_SLAIN: "has_slain.wav",
    }
    
    found_count = 0
    missing_files = []
    
    for event_type, filename in expected_files.items():
        file_path = audio_dir / filename
        if file_path.exists():
            file_size = file_path.stat().st_size
            duration = file_size / 44100 / 2 / 2  # Rough estimate for 16-bit stereo 44.1kHz
            print(f"   ✅ {filename:<20} ({file_size:,} bytes, ~{duration:.1f}s)")
            found_count += 1
        else:
            print(f"   ❌ {filename:<20} (MISSING)")
            missing_files.append(filename)
    
    print(f"\n📊 Audio Summary:")
    print(f"   ✅ Found: {found_count}/12 expected files")
    print(f"   ❌ Missing: {len(missing_files)} files")
    
    return len(missing_files) == 0

def check_visual_templates():
    """Check if all visual template files are present."""
    print("\n🖼️ Checking Visual Templates")
    print("=" * 50)
    
    templates_dir = Path("assets/templates")
    if not templates_dir.exists():
        print(f"❌ Templates directory not found: {templates_dir}")
        return False
    
    expected_files = {
        EventType.FIRST_BLOOD: "first_blood.png",
        EventType.DOUBLE_KILL: "double_kill.png", 
        EventType.TRIPLE_KILL: "triple_kill.png",
        EventType.MEGA_KILL: "mega_kill.png",
        EventType.MANIAC: "maniac.png",
        EventType.SAVAGE: "savage.png",
        EventType.KILLING_SPREE: "killing_spree.png",
        EventType.MONSTER_KILL: "monster_kill.png",
        EventType.GODLIKE: "godlike.png",
        EventType.LEGENDARY: "legendary.png",
        EventType.UNSTOPPABLE: "unstoppable.png",
        EventType.HAS_SLAIN: "has_slain.png",
    }
    
    found_count = 0
    missing_files = []
    
    for event_type, filename in expected_files.items():
        file_path = templates_dir / filename
        if file_path.exists():
            file_size = file_path.stat().st_size
            print(f"   ✅ {filename:<20} ({file_size:,} bytes)")
            found_count += 1
        else:
            print(f"   ❌ {filename:<20} (MISSING)")
            missing_files.append(filename)
    
    print(f"\n📊 Templates Summary:")
    print(f"   ✅ Found: {found_count}/12 expected files")
    print(f"   ❌ Missing: {len(missing_files)} files")
    
    return len(missing_files) == 0

def check_dependencies():
    """Check if key dependencies can be imported."""
    print("\n📦 Checking Dependencies")
    print("=" * 50)
    
    dependencies = [
        ("fastapi", "FastAPI"),
        ("uvicorn", "Uvicorn"),
        ("redis", "Redis"),
        ("cv2", "OpenCV"),
        ("librosa", "Librosa"),
        ("numpy", "NumPy"),
        ("scipy", "SciPy"),
        ("boto3", "Boto3"),
    ]
    
    all_imported = True
    for module, name in dependencies:
        try:
            __import__(module)
            print(f"   ✅ {name}")
        except ImportError:
            print(f"   ❌ {name} (not installed)")
            all_imported = False
    
    return all_imported

def check_configuration():
    """Check if configuration files exist."""
    print("\n⚙️ Checking Configuration")
    print("=" * 50)
    
    config_files = [
        ".env",
        "backend/shared/models.py",
        "backend/requirements.txt",
        "start_services.py",
    ]
    
    all_exist = True
    for file_path in config_files:
        if Path(file_path).exists():
            print(f"   ✅ {file_path}")
        else:
            print(f"   ❌ {file_path} (missing)")
            all_exist = False
    
    return all_exist

def main():
    """Run complete setup validation."""
    print("🚀 ML Highlights Complete Setup Validation")
    print("=" * 60)
    
    # Run all checks
    audio_ok = check_audio_files()
    templates_ok = check_visual_templates()
    deps_ok = check_dependencies()
    config_ok = check_configuration()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 COMPLETE SETUP SUMMARY")
    print("=" * 60)
    
    checks = [
        ("Audio Files", audio_ok),
        ("Visual Templates", templates_ok),
        ("Dependencies", deps_ok),
        ("Configuration", config_ok),
    ]
    
    all_passed = True
    for check_name, passed in checks:
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{check_name:<20}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 60)
    if all_passed:
        print("🎉 ALL CHECKS PASSED! Your ML Highlights system is ready!")
        print("\n📋 Next steps:")
        print("   1. Start services: python start_services.py")
        print("   2. Upload a Mobile Legends video for processing")
        print("   3. Check generated clips in your Backblaze B2 bucket")
    else:
        print("⚠️ Some checks failed. Please fix the issues above.")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
