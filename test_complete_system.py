#!/usr/bin/env python3
"""
Test the complete ultra-fast video processing system.
"""

import sys
import os
import asyncio
import tempfile

# Add backend to path
sys.path.append('backend')

async def test_complete_system():
    """Test the complete system with ultra-fast processing."""
    print("🚀 Testing Complete Ultra-Fast Video Processing System")
    print("=" * 60)
    
    try:
        from worker.video_processor import VideoProcessor
        from shared.models import ProcessingJob, JobStatus
        from shared.storage import get_storage_client
        import uuid
        from datetime import datetime
        
        # Test video path
        video_path = r"assets\samples\Hardest Gusion match in SOLO Q _ Mobile Legends.mp4"
        
        if not os.path.exists(video_path):
            print(f"❌ Test video not found: {video_path}")
            return False
        
        print(f"✅ Test video found: {video_path}")
        
        # Create a mock job
        job_id = str(uuid.uuid4())
        file_size = os.path.getsize(video_path)
        job = ProcessingJob(
            job_id=job_id,
            original_filename="test_video.mp4",
            file_size=file_size,
            status=JobStatus.QUEUED,
            created_at=datetime.utcnow()
        )
        
        print(f"📋 Created test job: {job_id}")
        
        # Create video processor with ultra-fast settings
        processor = VideoProcessor(
            assets_path="assets",
            temp_path=None
        )
        
        print(f"✅ VideoProcessor created with optimizations:")
        print(f"📂 Assets path: {processor.assets_path}")
        print(f"📁 Temp path: {processor.temp_path}")
        print(f"⚡ Fast mode: ENABLED")
        print(f"🎯 Events: 12 kill events only")
        print(f"🖼️ Visual: 0.5 fps, single scale")
        print(f"🎬 Clips: Stream copy mode (100x faster)")
        
        # Upload video to storage first
        storage_client = get_storage_client()
        s3_key = f"raw/{job_id}/test_video.mp4"
        
        print(f"\n📤 Uploading video to storage...")
        upload_success = await storage_client.upload_file(video_path, s3_key)
        if not upload_success:
            print(f"❌ Failed to upload video")
            return False
        
        print(f"✅ Video uploaded: {s3_key}")
        
        # Process video with ultra-fast mode
        print(f"\n🔄 Starting ULTRA-FAST video processing...")
        start_time = asyncio.get_event_loop().time()
        
        try:
            result = await processor.process_video(job, s3_key, fast_mode=True)
            
            end_time = asyncio.get_event_loop().time()
            total_time = end_time - start_time
            
            print(f"\n🎉 Processing completed!")
            print(f"📊 Status: {result.status}")
            print(f"⏱️ Total processing time: {total_time:.2f} seconds")
            print(f"🎬 Generated clips: {len(result.generated_clips)}")
            print(f"🎵 Audio events detected: {len([c for c in result.generated_clips if c.detection_method == 'audio'])}")
            print(f"👁️ Visual events detected: {len([c for c in result.generated_clips if c.detection_method == 'visual'])}")
            
            if result.generated_clips:
                print(f"\n🎯 Detected Events:")
                for i, clip in enumerate(result.generated_clips, 1):
                    print(f"  {i}. {clip.event_type} at {clip.timestamp:.1f}s (confidence: {clip.confidence:.2f})")
            
            # Performance analysis
            if total_time < 120:  # 2 minutes
                print(f"\n🏆 EXCELLENT: Processing completed in under 2 minutes!")
                print(f"🚀 System meets the 5-minute target with room to spare!")
            elif total_time < 300:  # 5 minutes
                print(f"\n✅ SUCCESS: Processing completed within 5-minute target!")
            else:
                print(f"\n⚠️ SLOW: Processing took longer than 5-minute target")
            
            return result.status == JobStatus.COMPLETED
            
        except Exception as process_error:
            end_time = asyncio.get_event_loop().time()
            total_time = end_time - start_time
            print(f"❌ Processing failed after {total_time:.2f}s: {process_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ System test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    return asyncio.run(test_complete_system())

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Complete ultra-fast system test PASSED!")
        print("🚀 System ready for production use!")
        sys.exit(0)
    else:
        print("\n❌ Complete system test FAILED!")
        sys.exit(1)
