"""Redis queue client for job management."""

import json
import asyncio
from typing import List, Optional
from datetime import datetime
import redis.asyncio as redis
import structlog

from shared.models import ProcessingJob, JobStatus
from shared.config import get_settings

logger = structlog.get_logger()


class QueueClient:
    """Redis-based queue client for managing processing jobs."""
    
    def __init__(self):
        self.settings = get_settings()
        self.redis_client = None
        self._connection_pool = None
    
    async def _get_redis(self) -> redis.Redis:
        """Get Redis client with connection pooling."""
        if self.redis_client is None:
            self._connection_pool = redis.ConnectionPool.from_url(
                self.settings.redis_url,
                max_connections=20,
                retry_on_timeout=True
            )
            self.redis_client = redis.Redis(connection_pool=self._connection_pool)
        return self.redis_client
    
    async def ping(self) -> bool:
        """Test Redis connection."""
        try:
            redis_client = await self._get_redis()
            await redis_client.ping()
            return True
        except Exception as e:
            logger.error("Redis ping failed", error=str(e))
            return False
    
    async def store_job(self, job: ProcessingJob) -> bool:
        """Store a job in Redis."""
        try:
            redis_client = await self._get_redis()
            job_key = f"job:{job.job_id}"
            job_data = job.model_dump_json()
            
            # Store job data
            await redis_client.set(job_key, job_data)
            
            # Add to job index
            await redis_client.zadd(
                "jobs:index",
                {job.job_id: job.created_at.timestamp()}
            )
            
            # Add to status index
            await redis_client.sadd(f"jobs:status:{job.status.value}", job.job_id)
            
            logger.info("Stored job in Redis", job_id=job.job_id, status=job.status)
            return True
            
        except Exception as e:
            logger.error("Failed to store job", job_id=job.job_id, error=str(e))
            return False
    
    async def get_job(self, job_id: str) -> Optional[ProcessingJob]:
        """Retrieve a job from Redis."""
        try:
            redis_client = await self._get_redis()
            job_key = f"job:{job_id}"
            job_data = await redis_client.get(job_key)
            
            if job_data:
                job_dict = json.loads(job_data)
                return ProcessingJob(**job_dict)
            return None
            
        except Exception as e:
            logger.error("Failed to get job", job_id=job_id, error=str(e))
            return None
    
    async def update_job(self, job: ProcessingJob) -> bool:
        """Update a job in Redis."""
        try:
            redis_client = await self._get_redis()
            job_key = f"job:{job.job_id}"
            
            # Get old job to update indices
            old_job_data = await redis_client.get(job_key)
            old_status = None
            if old_job_data:
                old_job = ProcessingJob(**json.loads(old_job_data))
                old_status = old_job.status
            
            # Update job data
            job_data = job.model_dump_json()
            await redis_client.set(job_key, job_data)
            
            # Update status indices if status changed
            if old_status and old_status != job.status:
                await redis_client.srem(f"jobs:status:{old_status.value}", job.job_id)
                await redis_client.sadd(f"jobs:status:{job.status.value}", job.job_id)
            
            logger.info("Updated job in Redis", job_id=job.job_id, status=job.status)
            return True
            
        except Exception as e:
            logger.error("Failed to update job", job_id=job.job_id, error=str(e))
            return False
    
    async def enqueue_job(self, job_id: str, s3_key: str) -> bool:
        """Enqueue a job for processing."""
        try:
            redis_client = await self._get_redis()
            
            # Add to processing queue
            job_payload = {
                "job_id": job_id,
                "s3_key": s3_key,
                "enqueued_at": datetime.utcnow().isoformat()
            }
            
            await redis_client.lpush("queue:processing", json.dumps(job_payload))
            
            logger.info("Enqueued job for processing", job_id=job_id, s3_key=s3_key)
            return True
            
        except Exception as e:
            logger.error("Failed to enqueue job", job_id=job_id, error=str(e))
            return False
    
    async def dequeue_job(self, timeout: int = 10) -> Optional[dict]:
        """Dequeue a job for processing (blocking)."""
        try:
            redis_client = await self._get_redis()
            
            # Blocking pop from queue
            result = await redis_client.brpop("queue:processing", timeout=timeout)
            if result:
                queue_name, job_data = result
                return json.loads(job_data)
            return None
            
        except Exception as e:
            logger.error("Failed to dequeue job", error=str(e))
            return None
    
    async def list_jobs(
        self, 
        limit: int = 50, 
        offset: int = 0, 
        status: Optional[JobStatus] = None
    ) -> List[ProcessingJob]:
        """List jobs with optional filtering."""
        try:
            redis_client = await self._get_redis()
            
            if status:
                # Get jobs by status
                job_ids = await redis_client.smembers(f"jobs:status:{status.value}")
                job_ids = list(job_ids)
            else:
                # Get all jobs from index (sorted by creation time)
                job_ids = await redis_client.zrevrange(
                    "jobs:index", 
                    offset, 
                    offset + limit - 1
                )
            
            # Fetch job data
            jobs = []
            for job_id in job_ids[offset:offset + limit]:
                if isinstance(job_id, bytes):
                    job_id = job_id.decode('utf-8')
                
                job = await self.get_job(job_id)
                if job:
                    jobs.append(job)
            
            return jobs
            
        except Exception as e:
            logger.error("Failed to list jobs", error=str(e))
            return []
    
    async def get_queue_length(self) -> int:
        """Get the current queue length."""
        try:
            redis_client = await self._get_redis()
            return await redis_client.llen("queue:processing")
        except Exception as e:
            logger.error("Failed to get queue length", error=str(e))
            return 0
    
    async def cleanup_old_jobs(self, max_age_days: int = 7) -> int:
        """Clean up old completed/failed jobs."""
        try:
            redis_client = await self._get_redis()
            cutoff_timestamp = (datetime.utcnow().timestamp() - (max_age_days * 24 * 3600))
            
            # Get old job IDs
            old_job_ids = await redis_client.zrangebyscore(
                "jobs:index", 
                0, 
                cutoff_timestamp
            )
            
            cleaned_count = 0
            for job_id in old_job_ids:
                if isinstance(job_id, bytes):
                    job_id = job_id.decode('utf-8')
                
                # Get job to check status
                job = await self.get_job(job_id)
                if job and job.status in [JobStatus.COMPLETED, JobStatus.FAILED]:
                    # Remove from all indices
                    await redis_client.delete(f"job:{job_id}")
                    await redis_client.zrem("jobs:index", job_id)
                    await redis_client.srem(f"jobs:status:{job.status.value}", job_id)
                    cleaned_count += 1
            
            logger.info("Cleaned up old jobs", count=cleaned_count, max_age_days=max_age_days)
            return cleaned_count
            
        except Exception as e:
            logger.error("Failed to cleanup old jobs", error=str(e))
            return 0


# Global queue client instance
_queue_client = None


async def get_queue_client() -> QueueClient:
    """Get the global queue client instance."""
    global _queue_client
    if _queue_client is None:
        _queue_client = QueueClient()
    return _queue_client
