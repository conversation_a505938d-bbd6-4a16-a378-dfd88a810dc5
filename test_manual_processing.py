#!/usr/bin/env python3
"""
Manually test video processing with the latest job.
"""

import sys
import os
import asyncio

# Add backend to path
sys.path.append('backend')

async def test_manual_processing():
    """Test processing the latest job manually."""
    print("🎬 Testing Manual Video Processing")
    print("=" * 50)
    
    try:
        from gateway.queue import get_queue_client
        from worker.video_processor import VideoProcessor
        from shared.storage import get_storage_client
        
        # Get queue and storage clients
        queue_client = await get_queue_client()
        storage_client = get_storage_client()
        
        # Get the latest job
        job_id = "49fa96e5-0336-49d3-8af5-74437e5ea676"  # Latest uploaded job
        print(f"📋 Testing job: {job_id}")
        
        job = await queue_client.get_job(job_id)
        if not job:
            print(f"❌ Job {job_id} not found")
            return False
        
        print(f"✅ Job found: {job.original_filename}")
        print(f"📊 Status: {job.status}")
        
        # Create video processor with correct paths
        processor = VideoProcessor(
            assets_path="assets",  # Correct path from project root
            temp_path=None  # Use system temp
        )
        
        print(f"✅ VideoProcessor created")
        print(f"📂 Assets path: {processor.assets_path}")
        print(f"📁 Temp path: {processor.temp_path}")
        
        # Test processing (this will show us the exact error)
        print(f"\n🔄 Starting video processing...")
        try:
            # Generate S3 key for the job
            s3_key = f"raw/{job.job_id}/{job.original_filename}"
            print(f"📁 S3 Key: {s3_key}")

            result = await processor.process_video(job, s3_key)
            print(f"✅ Processing completed!")
            print(f"📊 Result: {result}")
            return True
        except Exception as process_error:
            print(f"❌ Processing failed: {process_error}")
            import traceback
            traceback.print_exc()
            return False
        
    except Exception as e:
        print(f"❌ Setup failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    return asyncio.run(test_manual_processing())

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Manual processing test completed!")
        sys.exit(0)
    else:
        print("\n❌ Manual processing test failed!")
        sys.exit(1)
