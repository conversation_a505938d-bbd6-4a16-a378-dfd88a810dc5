# Mobile Legends Highlight Extractor

A lightweight web service that automatically extracts TikTok-ready highlight clips from Mobile Legends match recordings.

## Architecture

```
┌──────────────┐        POST /upload (presigned)       ┌──────────────┐
│  React SPA   │ ─────────────────────────────────────▶│   FastAPI    │
│  (Vite)      │ ⬑⬑⬑⬑ /status/{job_id}  ⬑⬑⬑⬑⬑⬑⬑⬑       │   Gateway    │
└──────────────┘                                        └─────▲───────┘
                                                               │enqueue
                                                               ▼
                                                        ┌──────────────┐
                                                        │  Redis →     │
                                                        │  RQ / Faktory│
                                                        └─────▲────────┘
                                                               │pop
┌────────────────┐  S3: raw/ , clips/ , zips/                ▼
│ Async Worker   │──fetch→ /tmp/raw.mp4                ┌──────────────┐
│  (Docker)      │←─store─ clip_i.mp4, …               │   S3 / GCS   │
│  pipeline.py   │──upload zip & playlist.json ──────▶ └──────────────┘
└────────────────┘
```

## Features

- **Audio Event Detection**: Identifies announcer callouts (Double Kill, Triple Kill, Savage, etc.)
- **Visual Template Matching**: Detects kill-feeds, objectives (<PERSON>/<PERSON>), and crystal destruction
- **Automatic Clip Generation**: Creates 6-second clips with 9:16 aspect ratio
- **Scalable Processing**: Horizontal scaling via Redis queue
- **Web Interface**: Upload, track progress, preview and download clips

## Performance Targets

- Process 20-minute match in ≤60 seconds on modest hardware (no GPU required)
- Support files up to 2GB
- Horizontal scalability via queue depth

## Project Structure

```
ml-highlights/
├── backend/
│   ├── gateway/          # FastAPI web server
│   ├── worker/           # Video processing pipeline
│   ├── shared/           # Common utilities and models
│   └── requirements.txt
├── frontend/             # React SPA with Vite
├── docker/               # Docker configurations
├── assets/               # Reference audio/visual templates
│   ├── audio/            # Announcer voice samples
│   └── templates/        # Visual template images
└── tests/
```

## Quick Start

1. **Prerequisites**
   - Python 3.11+
   - Docker
   - FFmpeg
   - Git

2. **Installation**
   ```bash
   # Clone repository
   git clone <repository-url>
   cd ml-highlights

   # Install Python dependencies
   pip install -r backend/requirements.txt

   # Copy environment file
   cp .env.example .env
   # Edit .env with your AWS S3 credentials
   ```

3. **Setup Reference Assets**
   ```bash
   # Create reference audio and visual templates
   # See assets/README.md for detailed instructions
   mkdir -p assets/audio assets/templates

   # Add your Mobile Legends reference files:
   # - assets/audio/*.wav (announcer voices)
   # - assets/templates/*.png (UI elements)
   ```

4. **Start Services**
   ```bash
   # Easy way - start all services
   python start_services.py

   # Or manually:
   # Terminal 1: Redis
   docker run -d --name ml-highlights-redis -p 6379:6379 redis:7-alpine

   # Terminal 2: API Gateway
   python -m uvicorn backend.gateway.main:app --reload

   # Terminal 3: Worker
   python -m backend.worker.main
   ```

5. **Test the API**
   ```bash
   # Test API endpoints
   python test_api.py

   # Test processing pipeline
   python tests/test_pipeline.py
   ```

6. **Upload Video**
   ```bash
   # Using curl (replace with your video file)
   curl -X POST "http://localhost:8000/upload?filename=match.mp4&file_size=1000000"

   # Check status
   curl "http://localhost:8000/status/{job_id}"
   ```

## API Documentation

Once the services are running, visit:
- **API Documentation**: http://localhost:8000/docs
- **Health Check**: http://localhost:8000/health

### Key Endpoints

- `POST /upload` - Upload video for processing
- `GET /status/{job_id}` - Check processing status
- `GET /jobs` - List all jobs

## Configuration

Key environment variables in `.env`:

```bash
# AWS S3 (required for production)
S3_BUCKET=your-bucket-name
AWS_ACCESS_KEY_ID=your-access-key
AWS_SECRET_ACCESS_KEY=your-secret-key
AWS_REGION=us-east-1

# Processing settings
AUDIO_SIMILARITY_THRESHOLD=0.83
VISUAL_SIMILARITY_THRESHOLD=0.90
CLIP_DURATION=6.0

# Development
DEBUG=true
LOG_LEVEL=INFO
```

## Troubleshooting

### Common Issues

1. **FFmpeg not found**
   ```bash
   # Ubuntu/Debian
   sudo apt install ffmpeg

   # macOS
   brew install ffmpeg

   # Windows
   # Download from https://ffmpeg.org/
   ```

2. **Redis connection failed**
   ```bash
   # Start Redis manually
   docker run -d -p 6379:6379 redis:alpine
   ```

3. **No events detected**
   - Check that reference assets exist in `assets/` directory
   - Verify audio/visual templates match your game version
   - Adjust similarity thresholds in configuration

4. **S3 upload errors**
   - Verify AWS credentials in `.env`
   - Check S3 bucket permissions
   - For development, you can mock S3 operations

### Performance Tuning

- Adjust `AUDIO_SIMILARITY_THRESHOLD` and `VISUAL_SIMILARITY_THRESHOLD`
- Modify `CLIP_DURATION` and padding settings
- Scale workers horizontally by running multiple worker processes
- Use faster storage for temporary files

## Development

### Project Structure
```
ml-highlights/
├── backend/
│   ├── gateway/          # FastAPI web server
│   ├── worker/           # Video processing pipeline
│   ├── shared/           # Common utilities and models
│   └── requirements.txt
├── assets/               # Reference audio/visual templates
├── docker/               # Docker configurations
├── tests/                # Test scripts
├── start_services.py     # Development server launcher
└── test_api.py          # API test script
```

### Adding New Event Types

1. Add event type to `shared/models.py`
2. Add reference assets to `assets/`
3. Update priority in `EVENT_PRIORITY` dict
4. Test with sample videos

### Contributing

1. Fork the repository
2. Create a feature branch
3. Add tests for new functionality
4. Submit a pull request

## License

[Add your license here]
