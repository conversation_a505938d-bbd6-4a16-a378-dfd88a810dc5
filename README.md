# Mobile Legends Highlight Extractor

A lightweight web service that automatically extracts TikTok-ready highlight clips from Mobile Legends match recordings.

## Architecture

```
┌──────────────┐        POST /upload (presigned)       ┌──────────────┐
│  React SPA   │ ─────────────────────────────────────▶│   FastAPI    │
│  (Vite)      │ ⬑⬑⬑⬑ /status/{job_id}  ⬑⬑⬑⬑⬑⬑⬑⬑       │   Gateway    │
└──────────────┘                                        └─────▲───────┘
                                                               │enqueue
                                                               ▼
                                                        ┌──────────────┐
                                                        │  Redis →     │
                                                        │  RQ / Faktory│
                                                        └─────▲────────┘
                                                               │pop
┌────────────────┐  S3: raw/ , clips/ , zips/                ▼
│ Async Worker   │──fetch→ /tmp/raw.mp4                ┌──────────────┐
│  (Docker)      │←─store─ clip_i.mp4, …               │   S3 / GCS   │
│  pipeline.py   │──upload zip & playlist.json ──────▶ └──────────────┘
└────────────────┘
```

## Features

- **Audio Event Detection**: Identifies announcer callouts (Double Kill, Triple Kill, Savage, etc.)
- **Visual Template Matching**: Detects kill-feeds, objectives (<PERSON>/<PERSON>), and crystal destruction
- **Automatic Clip Generation**: Creates 6-second clips with 9:16 aspect ratio
- **Scalable Processing**: Horizontal scaling via Redis queue
- **Web Interface**: Upload, track progress, preview and download clips

## Performance Targets

- Process 20-minute match in ≤60 seconds on modest hardware (no GPU required)
- Support files up to 2GB
- Horizontal scalability via queue depth

## Project Structure

```
ml-highlights/
├── backend/
│   ├── gateway/          # FastAPI web server
│   ├── worker/           # Video processing pipeline
│   ├── shared/           # Common utilities and models
│   └── requirements.txt
├── frontend/             # React SPA with Vite
├── docker/               # Docker configurations
├── assets/               # Reference audio/visual templates
│   ├── audio/            # Announcer voice samples
│   └── templates/        # Visual template images
└── tests/
```

## Quick Start

1. **Setup Environment**
   ```bash
   # Install dependencies
   pip install -r backend/requirements.txt
   cd frontend && npm install
   
   # Start Redis
   docker run -d -p 6379:6379 redis:alpine
   ```

2. **Start Services**
   ```bash
   # Terminal 1: API Gateway
   cd backend && uvicorn gateway.main:app --reload
   
   # Terminal 2: Worker
   cd backend && python -m worker.main
   
   # Terminal 3: Frontend
   cd frontend && npm run dev
   ```

3. **Upload Video**
   - Open http://localhost:5173
   - Upload Mobile Legends .mp4 file
   - Wait for processing and download highlights

## Development

See individual component READMEs for detailed setup and development instructions.
