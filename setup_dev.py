#!/usr/bin/env python3
"""Development setup script for ML Highlights."""

import os
import subprocess
import sys
from pathlib import Path


def check_python_version():
    """Check Python version."""
    if sys.version_info < (3, 11):
        print("❌ Python 3.11+ required")
        return False
    print(f"✅ Python {sys.version_info.major}.{sys.version_info.minor}")
    return True


def check_system_dependencies():
    """Check system dependencies."""
    print("🔍 Checking system dependencies...")
    
    dependencies = {
        "docker": ["docker", "--version"],
        "ffmpeg": ["ffmpeg", "-version"],
        "git": ["git", "--version"]
    }
    
    missing = []
    for name, cmd in dependencies.items():
        try:
            subprocess.run(cmd, capture_output=True, check=True)
            print(f"✅ {name}")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print(f"❌ {name}")
            missing.append(name)
    
    if missing:
        print(f"\n❌ Missing dependencies: {', '.join(missing)}")
        print("\nInstallation instructions:")
        print("- Docker: https://docs.docker.com/get-docker/")
        print("- FFmpeg: https://ffmpeg.org/download.html")
        print("- Git: https://git-scm.com/downloads")
        return False
    
    return True


def install_python_dependencies():
    """Install Python dependencies."""
    print("📦 Installing Python dependencies...")
    
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True)
        print("✅ Python dependencies installed")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Failed to install dependencies: {e}")
        return False


def setup_environment():
    """Setup environment file."""
    print("⚙️ Setting up environment...")
    
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        # Copy example env file
        with open(env_example) as f:
            content = f.read()
        
        with open(env_file, "w") as f:
            f.write(content)
        
        print("✅ Created .env file from template")
        print("⚠️ Please edit .env with your storage credentials")
        print("   - For Backblaze B2: See BACKBLAZE_SETUP.md")
        print("   - For AWS S3: Use standard AWS credentials")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("❌ No .env.example file found")
        return False
    
    return True


def create_directories():
    """Create necessary directories."""
    print("📁 Creating directories...")
    
    directories = [
        "assets/audio",
        "assets/templates", 
        "temp",
        "logs"
    ]
    
    for directory in directories:
        Path(directory).mkdir(parents=True, exist_ok=True)
        print(f"✅ {directory}")
    
    return True


def setup_assets():
    """Setup asset directories with instructions."""
    print("🎨 Setting up assets...")
    
    # Create placeholder files with instructions
    audio_readme = Path("assets/audio/README.md")
    if not audio_readme.exists():
        with open(audio_readme, "w") as f:
            f.write("""# Audio Reference Files

Place WAV files here for each Mobile Legends announcer event:

- `double_kill.wav` - "Double Kill!" announcer
- `triple_kill.wav` - "Triple Kill!" announcer  
- `maniac.wav` - "Maniac!" announcer
- `savage.wav` - "Savage!" announcer
- `first_blood.wav` - "First Blood!" announcer
- `killing_spree.wav` - "Killing Spree!" announcer

## Requirements:
- Format: WAV, 22050 Hz, mono
- Duration: 1-3 seconds
- Clean audio without background music

## How to create:
1. Record Mobile Legends gameplay
2. Extract audio clips of announcer voices
3. Use audio editing software to isolate clean samples
4. Export as WAV with correct format
""")
    
    templates_readme = Path("assets/templates/README.md")
    if not templates_readme.exists():
        with open(templates_readme, "w") as f:
            f.write("""# Visual Template Files

Place PNG files here for each Mobile Legends visual event:

- `double_kill_feed.png` - Kill feed notification
- `triple_kill_feed.png` - Kill feed notification
- `maniac_feed.png` - Kill feed notification
- `savage_feed.png` - Kill feed notification
- `first_blood_feed.png` - Kill feed notification
- `lord_banner.png` - Lord kill banner
- `turtle_banner.png` - Turtle kill banner
- `crystal_burst.png` - Crystal destruction effect
- `tower_destroy.png` - Tower destruction notification

## Requirements:
- Format: PNG (with transparency if needed)
- Size: Cropped to just the UI element
- Quality: Clear, high-contrast images

## How to create:
1. Take screenshots during gameplay when events occur
2. Crop to just the relevant UI element
3. Save as PNG
4. Test with different resolutions
""")
    
    print("✅ Asset directories setup with instructions")
    return True


def test_installation():
    """Test the installation."""
    print("🧪 Testing installation...")
    
    # Test imports
    try:
        import fastapi
        import uvicorn
        import redis
        import cv2
        import librosa
        print("✅ All Python packages importable")
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    
    # Test Redis connection (if running)
    try:
        import redis as redis_lib
        r = redis_lib.Redis(host='localhost', port=6379, socket_timeout=1)
        r.ping()
        print("✅ Redis connection successful")
    except Exception:
        print("⚠️ Redis not running (will start automatically)")
    
    return True


def main():
    """Main setup function."""
    print("🚀 ML Highlights Development Setup\n")
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Check system dependencies
    if not check_system_dependencies():
        return False
    
    print()
    
    # Install Python dependencies
    if not install_python_dependencies():
        return False
    
    print()
    
    # Setup environment
    if not setup_environment():
        return False
    
    # Create directories
    if not create_directories():
        return False
    
    # Setup assets
    if not setup_assets():
        return False
    
    print()
    
    # Test installation
    if not test_installation():
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next steps:")
    print("1. Add reference assets to assets/ directories")
    print("2. Configure storage credentials in .env:")
    print("   - For Backblaze B2: See BACKBLAZE_SETUP.md")
    print("   - For AWS S3: Use standard AWS credentials")
    print("3. Start services: python start_services.py")
    print("4. Test API: python test_api.py")
    print("5. Test pipeline: python tests/test_pipeline.py")
    
    return True


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
