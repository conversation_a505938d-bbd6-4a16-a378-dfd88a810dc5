# S3-Compatible Storage Configuration
# Choose storage provider: "aws" or "backblaze"
STORAGE_PROVIDER=backblaze

# For Backblaze B2
S3_BUCKET=your-backblaze-bucket-name
S3_ACCESS_KEY_ID=your_backblaze_key_id
S3_SECRET_ACCESS_KEY=your_backblaze_application_key
S3_REGION=us-west-004
S3_ENDPOINT_URL=https://s3.us-west-004.backblazeb2.com

# For AWS S3 (alternative configuration)
# STORAGE_PROVIDER=aws
# S3_BUCKET=your-aws-bucket-name
# S3_ACCESS_KEY_ID=your_aws_access_key
# S3_SECRET_ACCESS_KEY=your_aws_secret_key
# S3_REGION=us-east-1
# S3_ENDPOINT_URL=

# Redis Configuration
REDIS_URL=redis://localhost:6379

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
VITE_API_URL=http://localhost:8000

# Worker Configuration
MAX_WORKERS=2
WORKER_TIMEOUT=300

# File Upload Limits
MAX_FILE_SIZE=**********  # 2GB in bytes
ALLOWED_EXTENSIONS=mp4,avi,mov,mkv

# Processing Configuration
AUDIO_SIMILARITY_THRESHOLD=0.83
VISUAL_SIMILARITY_THRESHOLD=0.90
CLIP_DURATION=6
CLIP_PADDING=2

# Development
DEBUG=true
LOG_LEVEL=INFO
