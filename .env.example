# AWS S3 Configuration
S3_BUCKET=ml-highlights-bucket
AWS_ACCESS_KEY_ID=your_access_key_here
AWS_SECRET_ACCESS_KEY=your_secret_key_here
AWS_REGION=us-east-1

# Redis Configuration
REDIS_URL=redis://localhost:6379

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
VITE_API_URL=http://localhost:8000

# Worker Configuration
MAX_WORKERS=2
WORKER_TIMEOUT=300

# File Upload Limits
MAX_FILE_SIZE=2147483648  # 2GB in bytes
ALLOWED_EXTENSIONS=mp4,avi,mov,mkv

# Processing Configuration
AUDIO_SIMILARITY_THRESHOLD=0.83
VISUAL_SIMILARITY_THRESHOLD=0.90
CLIP_DURATION=6
CLIP_PADDING=2

# Development
DEBUG=true
LOG_LEVEL=INFO
