#!/usr/bin/env python3
"""
Test Redis connection and queue operations.
"""

import sys
import os

# Add backend to path
sys.path.append('backend')

def test_redis_connection():
    """Test Redis connection."""
    print("🔴 Testing Redis connection...")
    
    try:
        from gateway.queue import get_queue_client
        import asyncio
        
        async def test_connection():
            queue_client = await get_queue_client()
            redis = await queue_client._get_redis()
            
            # Test basic Redis operations
            await redis.set("test_key", "test_value")
            value = await redis.get("test_key")
            await redis.delete("test_key")
            
            print(f"✅ Redis connection successful")
            print(f"✅ Test value: {value}")
            
            # Test queue operations
            jobs = await queue_client.get_pending_jobs()
            print(f"✅ Pending jobs: {len(jobs)}")
            
            for job in jobs:
                print(f"📋 Job: {job.job_id} - Status: {job.status}")
            
            return True
        
        return asyncio.run(test_connection())
        
    except Exception as e:
        print(f"❌ Redis connection failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_worker_imports():
    """Test worker imports."""
    print("\n🔄 Testing worker imports...")
    
    try:
        from worker.main import Worker
        print("✅ Worker class imported")
        
        # Test worker initialization
        worker = Worker()
        print("✅ Worker initialized")
        print(f"📂 Assets path: {worker.processor.assets_path}")
        print(f"📁 Temp path: {worker.processor.temp_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ Worker import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Testing Redis and Worker Connection")
    print("=" * 60)
    
    # Test Redis connection
    if not test_redis_connection():
        return 1
    
    # Test worker imports
    if not test_worker_imports():
        return 1
    
    print("\n🎉 All connection tests passed!")
    return 0

if __name__ == "__main__":
    sys.exit(main())
