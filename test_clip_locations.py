#!/usr/bin/env python3
"""
Test to find where clips are stored and provide download URLs.
"""

import sys
import os
import asyncio

# Add backend to path
sys.path.append('backend')

async def test_clip_locations():
    """Test where clips are stored and get download URLs."""
    print("📁 Testing Clip Storage Locations")
    print("=" * 50)
    
    try:
        from shared.storage import get_storage_client
        
        # Get storage client
        storage_client = get_storage_client()
        print(f"✅ Storage client connected")
        print(f"🪣 Bucket: {storage_client.bucket}")
        print(f"🌐 Endpoint: {storage_client.settings.s3_endpoint_url}")
        
        # List all objects in the bucket to see what's there
        print(f"\n📋 Listing all objects in bucket...")
        
        try:
            # Use boto3 to list objects
            import boto3
            
            s3_client = boto3.client(
                's3',
                endpoint_url=storage_client.settings.s3_endpoint_url,
                aws_access_key_id=storage_client.settings.s3_access_key_id,
                aws_secret_access_key=storage_client.settings.s3_secret_access_key,
                region_name=storage_client.settings.s3_region
            )

            response = s3_client.list_objects_v2(Bucket=storage_client.bucket)
            
            if 'Contents' in response:
                print(f"📦 Found {len(response['Contents'])} objects:")
                
                clips = []
                videos = []
                other = []
                
                for obj in response['Contents']:
                    key = obj['Key']
                    size = obj['Size'] / (1024 * 1024)  # MB
                    modified = obj['LastModified']
                    
                    if key.endswith('.mp4') and '/clips/' in key:
                        clips.append((key, size, modified))
                    elif key.endswith('.mp4'):
                        videos.append((key, size, modified))
                    else:
                        other.append((key, size, modified))
                
                if videos:
                    print(f"\n🎬 Raw Videos ({len(videos)}):")
                    for key, size, modified in videos:
                        print(f"  📹 {key} ({size:.1f}MB) - {modified}")
                
                if clips:
                    print(f"\n🎯 Generated Clips ({len(clips)}):")
                    for key, size, modified in clips:
                        print(f"  🎬 {key} ({size:.1f}MB) - {modified}")
                        
                        # Generate download URL
                        try:
                            download_url = await storage_client.generate_presigned_download_url(key, expires_in=3600)
                            print(f"     🔗 Download: {download_url}")
                        except Exception as e:
                            print(f"     ❌ Failed to generate URL: {e}")
                
                if other:
                    print(f"\n📄 Other Files ({len(other)}):")
                    for key, size, modified in other:
                        print(f"  📄 {key} ({size:.1f}MB) - {modified}")
                        
                        if key.endswith('.json'):
                            try:
                                download_url = await storage_client.generate_presigned_download_url(key, expires_in=3600)
                                print(f"     🔗 Download: {download_url}")
                            except Exception as e:
                                print(f"     ❌ Failed to generate URL: {e}")
                
                if not clips:
                    print(f"\n⚠️ No clips found in storage!")
                    print(f"💡 Clips should be in paths like: processed/{job_id}/clips/{clip_id}.mp4")
                
            else:
                print(f"📭 Bucket is empty - no objects found")
            
            return len(clips) > 0
            
        except Exception as e:
            print(f"❌ Failed to list objects: {e}")
            return False
        
    except Exception as e:
        print(f"❌ Storage test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    return asyncio.run(test_clip_locations())

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Found clips in storage!")
    else:
        print("\n❌ No clips found in storage!")
    sys.exit(0 if success else 1)
