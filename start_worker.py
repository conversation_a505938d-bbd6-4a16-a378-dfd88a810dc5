#!/usr/bin/env python3
"""
Worker startup script for ML Highlights.
Starts the background worker for video processing.
"""

import os
import sys
import subprocess
from pathlib import Path

def start_worker():
    """Start the background worker."""
    print("🔄 Starting ML Highlights Worker")
    print("=" * 50)
    
    # Change to backend directory
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False
    
    # Set environment variables
    os.environ["PYTHONPATH"] = str(Path.cwd() / "backend")
    
    print("📂 Backend directory:", backend_dir.absolute())
    print("🔄 Processing queued jobs...")
    print("📊 Will process videos and generate highlights")
    print("\n" + "="*50)
    print("🔄 Starting worker... (Press Ctrl+C to stop)")
    print("="*50)
    
    try:
        # Start worker from the backend directory
        cmd = [
            sys.executable, "-m", "worker.main"
        ]
        
        # Run from backend directory
        result = subprocess.run(cmd, cwd=backend_dir)
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Worker stopped by user")
        return True
    except Exception as e:
        print(f"❌ Failed to start worker: {e}")
        return False

def main():
    """Main function."""
    if start_worker():
        print("✅ Worker stopped successfully")
        return 0
    else:
        print("❌ Worker failed to start")
        return 1

if __name__ == "__main__":
    sys.exit(main())
