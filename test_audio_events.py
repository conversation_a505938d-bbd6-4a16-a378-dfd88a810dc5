#!/usr/bin/env python3
"""Test script specifically for the 11 audio events."""

import os
import sys
import asyncio

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from shared.models import EventType
from worker.audio_detector import AudioEventDetector


def test_audio_loading():
    """Test loading of all 11 audio reference files."""
    print("🎵 Testing Audio Reference Loading\n")
    
    detector = AudioEventDetector(assets_path="assets")
    
    # Expected audio events
    expected_events = [
        EventType.FIRST_BLOOD,
        EventType.DOUBLE_KILL,
        EventType.TRIPLE_KILL,
        EventType.MEGA_KILL,
        EventType.MANIAC,
        EventType.SAVAGE,
        EventType.KILLING_SPREE,
        EventType.MONSTER_KILL,
        EventType.GODLIKE,
        EventType.LEGENDARY,
        EventType.UNSTOPPABLE,
        EventType.HAS_SLAIN,
    ]
    
    print(f"📋 Expected {len(expected_events)} audio events")
    print(f"✅ Loaded {len(detector.reference_specs)} reference spectrograms")
    
    # Check which events were loaded
    loaded_events = set(detector.reference_specs.keys())
    expected_events_set = set(expected_events)
    
    # Show loaded events
    print("\n📂 Loaded Events:")
    for event in sorted(loaded_events, key=lambda x: x.value):
        print(f"   ✅ {event.value}")
    
    # Show missing events
    missing_events = expected_events_set - loaded_events
    if missing_events:
        print("\n❌ Missing Events:")
        for event in sorted(missing_events, key=lambda x: x.value):
            print(f"   ❌ {event.value} (file: {event.value}.wav)")
    
    # Show unexpected events
    extra_events = loaded_events - expected_events_set
    if extra_events:
        print("\n⚠️ Unexpected Events:")
        for event in sorted(extra_events, key=lambda x: x.value):
            print(f"   ⚠️ {event.value}")
    
    return len(loaded_events), len(missing_events)


def test_audio_files():
    """Test the actual audio files in the assets directory."""
    print("\n📁 Testing Audio Files\n")
    
    audio_dir = "assets/audio"
    if not os.path.exists(audio_dir):
        print(f"❌ Audio directory not found: {audio_dir}")
        return False
    
    # Expected files
    expected_files = [
        "first_blood.wav",
        "double_kill.wav", 
        "triple_kill.wav",
        "mega_kill.wav",
        "maniac.wav",
        "savage.wav",
        "killing_spree.wav",
        "monster_kill.wav",
        "godlike.wav",
        "legendary.wav",
        "unstoppable.wav",
        "has_slain.wav",
    ]
    
    # Check files
    found_files = []
    missing_files = []
    
    for filename in expected_files:
        file_path = os.path.join(audio_dir, filename)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            print(f"   ✅ {filename} ({file_size:,} bytes)")
            found_files.append(filename)
        else:
            print(f"   ❌ {filename} (missing)")
            missing_files.append(filename)
    
    # Check for extra files
    actual_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
    extra_files = [f for f in actual_files if f not in expected_files]
    
    if extra_files:
        print("\n⚠️ Extra Files:")
        for filename in extra_files:
            file_path = os.path.join(audio_dir, filename)
            file_size = os.path.getsize(file_path)
            print(f"   ⚠️ {filename} ({file_size:,} bytes)")
    
    print(f"\n📊 Summary:")
    print(f"   Found: {len(found_files)}/11 expected files")
    print(f"   Missing: {len(missing_files)} files")
    print(f"   Extra: {len(extra_files)} files")
    
    return len(missing_files) == 0


def test_event_priorities():
    """Test the event priority system."""
    print("\n🏆 Testing Event Priorities\n")
    
    from shared.models import EVENT_PRIORITY
    
    # Sort events by priority
    sorted_events = sorted(EVENT_PRIORITY.items(), key=lambda x: x[1], reverse=True)
    
    print("Event Priority Ranking (highest to lowest):")
    for i, (event, priority) in enumerate(sorted_events, 1):
        print(f"   {i:2d}. {event.value:<15} (priority: {priority:2d})")
    
    # Check for missing events
    all_audio_events = [
        EventType.FIRST_BLOOD, EventType.DOUBLE_KILL, EventType.TRIPLE_KILL,
        EventType.MEGA_KILL, EventType.MANIAC, EventType.SAVAGE,
        EventType.KILLING_SPREE, EventType.MONSTER_KILL, EventType.GODLIKE,
        EventType.LEGENDARY, EventType.UNSTOPPABLE, EventType.HAS_SLAIN
    ]
    
    missing_priorities = [e for e in all_audio_events if e not in EVENT_PRIORITY]
    if missing_priorities:
        print(f"\n❌ Events missing from priority list:")
        for event in missing_priorities:
            print(f"   ❌ {event.value}")
    else:
        print(f"\n✅ All audio events have priorities assigned")
    
    return len(missing_priorities) == 0


async def test_detection_simulation():
    """Simulate detection on a test audio file."""
    print("\n🧪 Testing Detection Simulation\n")
    
    # Check if we have a test audio file
    test_files = ["test_audio.wav", "sample.wav", "test.wav"]
    test_file = None
    
    for filename in test_files:
        if os.path.exists(filename):
            test_file = filename
            break
    
    if not test_file:
        print("⚠️ No test audio file found. Skipping detection test.")
        print("   Create a test file named 'test_audio.wav' to test detection.")
        return True
    
    print(f"🎵 Testing detection on: {test_file}")
    
    try:
        detector = AudioEventDetector(assets_path="assets")
        events = detector.detect_events(test_file)
        
        print(f"🎯 Detected {len(events)} events:")
        for event in events:
            print(f"   - {event.event_type.value} at {event.timestamp:.1f}s (confidence: {event.confidence:.3f})")
        
        return True
        
    except Exception as e:
        print(f"❌ Detection test failed: {e}")
        return False


async def main():
    """Main test function."""
    print("🚀 ML Highlights Audio Events Test\n")
    
    # Test 1: Audio file presence
    files_ok = test_audio_files()
    
    # Test 2: Audio loading
    loaded_count, missing_count = test_audio_loading()
    
    # Test 3: Event priorities
    priorities_ok = test_event_priorities()
    
    # Test 4: Detection simulation
    detection_ok = await test_detection_simulation()
    
    # Summary
    print("\n" + "="*50)
    print("📊 TEST SUMMARY")
    print("="*50)
    
    print(f"Audio Files:     {'✅ PASS' if files_ok else '❌ FAIL'}")
    print(f"Audio Loading:   {'✅ PASS' if missing_count == 0 else f'❌ FAIL ({missing_count} missing)'}")
    print(f"Event Priorities: {'✅ PASS' if priorities_ok else '❌ FAIL'}")
    print(f"Detection Test:  {'✅ PASS' if detection_ok else '❌ FAIL'}")
    
    all_passed = files_ok and missing_count == 0 and priorities_ok and detection_ok
    
    if all_passed:
        print("\n🎉 All tests passed! Your audio events are ready.")
    else:
        print("\n⚠️ Some tests failed. Check the issues above.")
    
    print(f"\n📈 Audio Events Status: {loaded_count}/11 events loaded")
    
    return all_passed


if __name__ == "__main__":
    asyncio.run(main())
