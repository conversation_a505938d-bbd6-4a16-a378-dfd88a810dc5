#!/usr/bin/env python3
"""
Test ultra-fast clip extraction directly without Red<PERSON>.
"""

import sys
import os
import asyncio
import tempfile

# Add backend to path
sys.path.append('backend')

async def test_ultra_fast_clips():
    """Test ultra-fast clip extraction."""
    print("⚡ Testing Ultra-Fast Clip Extraction")
    print("=" * 50)
    
    try:
        from worker.clip_generator import Clip<PERSON>enerator
        from shared.models import EventType
        
        # Test video path (use a sample video)
        video_path = r"assets\samples\Hardest Gusion match in SOLO Q _ Mobile Legends.mp4"
        
        if not os.path.exists(video_path):
            print(f"❌ Test video not found: {video_path}")
            return False
        
        print(f"✅ Test video found: {video_path}")
        
        # Create clip generator
        clip_generator = ClipGenerator()
        print(f"✅ ClipGenerator created")
        
        # Create temp directory for output
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 Temp directory: {temp_dir}")
            
            # Test parameters
            test_clips = [
                {"start": 10.0, "event": EventType.FIRST_BLOOD, "name": "first_blood_test.mp4"},
                {"start": 30.0, "event": EventType.DOUBLE_KILL, "name": "double_kill_test.mp4"},
                {"start": 60.0, "event": EventType.TRIPLE_KILL, "name": "triple_kill_test.mp4"},
            ]
            
            print(f"\n🎬 Testing {len(test_clips)} clip extractions...")
            
            total_start_time = asyncio.get_event_loop().time()
            
            for i, clip_info in enumerate(test_clips, 1):
                output_path = os.path.join(temp_dir, clip_info["name"])
                
                print(f"\n📹 Clip {i}/{len(test_clips)}: {clip_info['event'].value}")
                print(f"   ⏰ Start: {clip_info['start']}s")
                print(f"   📁 Output: {clip_info['name']}")
                
                start_time = asyncio.get_event_loop().time()
                
                # Extract clip using ultra-fast stream copy
                success = await clip_generator.generate_clip(
                    video_path=video_path,
                    output_path=output_path,
                    start_time=clip_info["start"],
                    event_type=clip_info["event"],
                    duration=10.0  # 10 second clips
                )
                
                end_time = asyncio.get_event_loop().time()
                extraction_time = end_time - start_time
                
                if success and os.path.exists(output_path):
                    file_size = os.path.getsize(output_path) / (1024 * 1024)  # MB
                    print(f"   ✅ Success! Time: {extraction_time:.2f}s, Size: {file_size:.1f}MB")
                else:
                    print(f"   ❌ Failed! Time: {extraction_time:.2f}s")
                    return False
            
            total_end_time = asyncio.get_event_loop().time()
            total_time = total_end_time - total_start_time
            
            print(f"\n🎉 All clips extracted successfully!")
            print(f"⏱️ Total time: {total_time:.2f} seconds")
            print(f"⚡ Average per clip: {total_time/len(test_clips):.2f} seconds")
            print(f"🚀 Speed: {'ULTRA-FAST' if total_time < 30 else 'FAST' if total_time < 60 else 'SLOW'}")
            
            # Performance analysis
            if total_time < 15:
                print(f"🏆 EXCELLENT: Can process full video in under 5 minutes!")
            elif total_time < 30:
                print(f"✅ GOOD: Should process full video in under 10 minutes")
            else:
                print(f"⚠️ SLOW: May take longer than desired for full video")
            
            return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    return asyncio.run(test_ultra_fast_clips())

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Ultra-fast clip extraction test completed!")
        sys.exit(0)
    else:
        print("\n❌ Ultra-fast clip extraction test failed!")
        sys.exit(1)
