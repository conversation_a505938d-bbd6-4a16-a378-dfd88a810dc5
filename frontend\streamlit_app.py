#!/usr/bin/env python3
"""
Streamlit frontend for ML Highlights system.
Simple interface for uploading Mobile Legends videos and viewing generated highlights.
"""

import streamlit as st
import requests
import time
import json
from pathlib import Path
import os
from datetime import datetime

# Configuration
API_BASE_URL = "http://localhost:8000"
UPLOAD_ENDPOINT = f"{API_BASE_URL}/upload"
STATUS_ENDPOINT = f"{API_BASE_URL}/status"

def init_session_state():
    """Initialize session state variables."""
    if 'uploaded_jobs' not in st.session_state:
        st.session_state.uploaded_jobs = []
    if 'current_job_id' not in st.session_state:
        st.session_state.current_job_id = None

def upload_video(video_file):
    """Upload video to the ML Highlights API."""
    try:
        files = {"file": (video_file.name, video_file.getvalue(), "video/mp4")}
        
        with st.spinner("Uploading video..."):
            response = requests.post(UPLOAD_ENDPOINT, files=files, timeout=60)
        
        if response.status_code == 200:
            result = response.json()
            job_id = result.get("job_id")
            st.success(f"✅ Video uploaded successfully! Job ID: {job_id}")
            
            # Add to session state
            job_info = {
                "job_id": job_id,
                "filename": video_file.name,
                "upload_time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "status": "processing"
            }
            st.session_state.uploaded_jobs.append(job_info)
            st.session_state.current_job_id = job_id
            
            return job_id
        else:
            st.error(f"❌ Upload failed: {response.text}")
            return None
            
    except requests.exceptions.ConnectionError:
        st.error("❌ Cannot connect to ML Highlights API. Make sure the backend is running.")
        return None
    except Exception as e:
        st.error(f"❌ Upload error: {str(e)}")
        return None

def check_job_status(job_id):
    """Check the status of a processing job."""
    try:
        response = requests.get(f"{STATUS_ENDPOINT}/{job_id}", timeout=10)
        
        if response.status_code == 200:
            return response.json()
        else:
            return {"status": "error", "message": f"Status check failed: {response.text}"}
            
    except requests.exceptions.ConnectionError:
        return {"status": "error", "message": "Cannot connect to API"}
    except Exception as e:
        return {"status": "error", "message": str(e)}

def display_job_status(job_info):
    """Display job status with progress indicators."""
    job_id = job_info["job_id"]
    status_data = check_job_status(job_id)
    
    col1, col2, col3 = st.columns([2, 1, 1])
    
    with col1:
        st.write(f"**{job_info['filename']}**")
        st.caption(f"Uploaded: {job_info['upload_time']}")
    
    with col2:
        status = status_data.get("status", "unknown")
        if status == "processing":
            st.warning("🔄 Processing")
        elif status == "completed":
            st.success("✅ Completed")
        elif status == "failed":
            st.error("❌ Failed")
        else:
            st.info(f"📋 {status}")
    
    with col3:
        if st.button("🔄 Refresh", key=f"refresh_{job_id}"):
            st.rerun()
    
    # Show detailed status
    if status_data.get("status") == "completed":
        highlights = status_data.get("highlights", [])
        if highlights:
            st.success(f"🎬 Generated {len(highlights)} highlight clips!")
            
            # Display highlights
            for i, highlight in enumerate(highlights):
                with st.expander(f"Highlight {i+1}: {highlight.get('event_type', 'Unknown').replace('_', ' ').title()}"):
                    col1, col2 = st.columns(2)
                    
                    with col1:
                        st.write(f"**Event:** {highlight.get('event_type', 'Unknown').replace('_', ' ').title()}")
                        st.write(f"**Timestamp:** {highlight.get('timestamp', 'Unknown')}s")
                        st.write(f"**Duration:** {highlight.get('duration', 'Unknown')}s")
                        if highlight.get('confidence'):
                            st.write(f"**Confidence:** {highlight.get('confidence', 0):.2f}")
                    
                    with col2:
                        clip_url = highlight.get('clip_url')
                        if clip_url:
                            st.write("**Download:**")
                            st.markdown(f"[📥 Download Clip]({clip_url})")
                        
                        # Show chained events if any
                        chained_events = highlight.get('chained_events', [])
                        if chained_events:
                            st.write("**Chain:**")
                            chain_text = " → ".join([e.replace('_', ' ').title() for e in chained_events])
                            st.caption(chain_text)
        else:
            st.info("No highlights detected in this video.")
    
    elif status_data.get("status") == "processing":
        progress = status_data.get("progress", 0)
        st.progress(progress / 100)
        st.caption(f"Progress: {progress}%")
        
        current_step = status_data.get("current_step", "Processing...")
        st.caption(current_step)
    
    elif status_data.get("status") == "failed":
        error_msg = status_data.get("message", "Unknown error")
        st.error(f"Processing failed: {error_msg}")
    
    st.divider()

def main():
    """Main Streamlit application."""
    st.set_page_config(
        page_title="ML Highlights",
        page_icon="🎮",
        layout="wide",
        initial_sidebar_state="expanded"
    )
    
    # Initialize session state
    init_session_state()
    
    # Header
    st.title("🎮 ML Highlights")
    st.subheader("Mobile Legends Highlight Extractor")
    st.markdown("Upload your Mobile Legends gameplay videos and get AI-generated highlight clips!")
    
    # Sidebar
    with st.sidebar:
        st.header("📊 System Info")
        
        # Check API status
        try:
            response = requests.get(f"{API_BASE_URL}/health", timeout=5)
            if response.status_code == 200:
                st.success("✅ API Online")
            else:
                st.error("❌ API Error")
        except:
            st.error("❌ API Offline")
        
        st.markdown("---")
        
        st.header("🎯 Supported Events")
        st.markdown("""
        **Multi-Kill Events:**
        - First Blood, Double Kill, Triple Kill
        - Mega Kill, Maniac, Savage
        
        **Kill Spree Events:**
        - Killing Spree, Monster Kill
        - Godlike, Legendary, Unstoppable
        
        **General:**
        - Has Slain
        """)
        
        st.markdown("---")
        st.caption("💾 Clips stored in Backblaze B2")
        st.caption("🎬 9:16 TikTok-ready format")
    
    # Main content
    tab1, tab2 = st.tabs(["📤 Upload Video", "📋 Job Status"])
    
    with tab1:
        st.header("Upload Mobile Legends Video")
        
        uploaded_file = st.file_uploader(
            "Choose a video file",
            type=['mp4', 'avi', 'mov', 'mkv'],
            help="Upload your Mobile Legends gameplay video (max 200MB)"
        )
        
        if uploaded_file is not None:
            # Show file info
            file_size = len(uploaded_file.getvalue())
            st.info(f"📁 File: {uploaded_file.name} ({file_size / (1024*1024):.1f} MB)")
            
            # Upload button
            if st.button("🚀 Upload & Process", type="primary"):
                job_id = upload_video(uploaded_file)
                if job_id:
                    st.balloons()
                    time.sleep(1)
                    st.rerun()
        
        # Quick tips
        with st.expander("💡 Tips for Best Results"):
            st.markdown("""
            - **Video Quality:** Higher quality videos work better
            - **Audio:** Make sure game audio is clear and not muted
            - **Length:** Shorter videos (5-15 minutes) process faster
            - **Format:** MP4 format is recommended
            - **Events:** The more kills/events in your video, the more highlights!
            """)
    
    with tab2:
        st.header("Processing Jobs")
        
        if st.session_state.uploaded_jobs:
            st.write(f"📊 Total jobs: {len(st.session_state.uploaded_jobs)}")
            
            # Auto-refresh toggle
            auto_refresh = st.checkbox("🔄 Auto-refresh every 10 seconds")
            
            if auto_refresh:
                time.sleep(10)
                st.rerun()
            
            st.markdown("---")
            
            # Display all jobs
            for job_info in reversed(st.session_state.uploaded_jobs):  # Most recent first
                display_job_status(job_info)
        else:
            st.info("No jobs yet. Upload a video to get started!")
            
            if st.button("🎮 Go to Upload"):
                st.switch_page("Upload Video")

if __name__ == "__main__":
    main()
