# ML Highlights Frontend

Simple Streamlit-based web interface for the ML Highlights system.

## Features

🎮 **Easy Video Upload**
- Drag & drop interface for Mobile Legends videos
- Support for MP4, AVI, MOV, MKV formats
- File size validation (max 200MB)

📊 **Real-time Job Tracking**
- Live status updates for processing jobs
- Progress indicators and current step display
- Auto-refresh option for hands-free monitoring

🎬 **Highlight Management**
- View all generated highlight clips
- Download links for each clip
- Event type and confidence scores
- Multi-kill chain visualization

🎯 **Event Detection Display**
- Shows all 12 supported event types
- Multi-kill events: First Blood, Double Kill, Triple Kill, Mega Kill, Maniac, Savage
- Kill spree events: Killing Spree, Monster Kill, Godlike, Legendary, Unstoppable
- General events: Has Slain

## Quick Start

### 1. Install Dependencies
```bash
pip install streamlit requests
```

### 2. Start the Frontend
```bash
# From the project root directory
python start_frontend.py

# Or manually
streamlit run frontend/streamlit_app.py
```

### 3. Access the Interface
- Open your browser to: http://localhost:8501
- Make sure the backend API is running at: http://localhost:8000

## Usage

### Upload a Video
1. Go to the "Upload Video" tab
2. Select your Mobile Legends gameplay video
3. Click "Upload & Process"
4. Get a job ID for tracking

### Monitor Progress
1. Switch to the "Job Status" tab
2. View real-time processing updates
3. Enable auto-refresh for hands-free monitoring
4. Download clips when processing completes

### Download Highlights
1. Completed jobs show generated highlights
2. Each highlight includes:
   - Event type (e.g., "Triple Kill")
   - Timestamp in original video
   - Clip duration
   - Confidence score
   - Download link to Backblaze B2

## Configuration

### API Endpoint
The frontend connects to the backend API at `http://localhost:8000` by default.

To change this, edit `frontend/streamlit_app.py`:
```python
API_BASE_URL = "http://your-api-server:8000"
```

### Upload Limits
- Maximum file size: 200MB
- Supported formats: MP4, AVI, MOV, MKV
- Recommended: MP4 format for best compatibility

### Theme
The interface uses a gaming-inspired color scheme:
- Primary color: #FF6B6B (red)
- Clean white background
- Clear status indicators

## Troubleshooting

### "API Offline" Error
- Make sure the backend services are running
- Check that Docker containers are up
- Verify the API endpoint URL

### Upload Fails
- Check file size (must be under 200MB)
- Ensure file format is supported
- Verify internet connection

### No Highlights Generated
- Check that the video contains Mobile Legends gameplay
- Ensure game audio is clear and not muted
- Verify the video has actual kill events

## Development

### File Structure
```
frontend/
├── streamlit_app.py      # Main Streamlit application
├── requirements.txt      # Python dependencies
├── .streamlit/
│   └── config.toml      # Streamlit configuration
└── README.md            # This file
```

### Adding Features
The Streamlit app is modular and easy to extend:
- Add new tabs in the main function
- Create new status display functions
- Extend the API integration

### Styling
Streamlit uses the configuration in `.streamlit/config.toml` for theming.
Custom CSS can be added using `st.markdown()` with `unsafe_allow_html=True`.

## API Integration

The frontend communicates with the backend via REST API:

- `POST /upload` - Upload video files
- `GET /status/{job_id}` - Check processing status
- `GET /health` - API health check

Response formats match the backend API specification.
