#!/usr/bin/env python3
"""Simple API test script for the ML Highlights service."""

import requests
import time
import json
import os
from typing import Optional


class APITester:
    """Test the ML Highlights API."""
    
    def __init__(self, base_url: str = "http://localhost:8000"):
        self.base_url = base_url
        self.session = requests.Session()
    
    def test_health(self) -> bool:
        """Test the health endpoint."""
        print("🏥 Testing health endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/health")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Health check passed: {data['status']}")
                return True
            else:
                print(f"❌ Health check failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Health check error: {e}")
            return False
    
    def test_upload(self, filename: str = "test_video.mp4") -> Optional[str]:
        """Test video upload endpoint."""
        print(f"📤 Testing upload endpoint with {filename}...")
        
        if not os.path.exists(filename):
            print(f"❌ Test file {filename} not found")
            return None
        
        file_size = os.path.getsize(filename)
        
        try:
            # Request upload URL
            response = self.session.post(
                f"{self.base_url}/upload",
                params={
                    "filename": filename,
                    "file_size": file_size
                }
            )
            
            if response.status_code == 200:
                data = response.json()
                job_id = data["job_id"]
                upload_url = data["upload_url"]
                
                print(f"✅ Upload URL generated for job: {job_id}")
                
                # Simulate file upload (in real scenario, upload to S3)
                print("⚠️ Note: This is a mock upload. In production, upload to the presigned URL.")
                
                return job_id
            else:
                print(f"❌ Upload request failed: {response.status_code}")
                print(response.text)
                return None
                
        except Exception as e:
            print(f"❌ Upload error: {e}")
            return None
    
    def test_status(self, job_id: str) -> bool:
        """Test job status endpoint."""
        print(f"📊 Testing status endpoint for job {job_id}...")
        
        try:
            response = self.session.get(f"{self.base_url}/status/{job_id}")
            
            if response.status_code == 200:
                data = response.json()
                status = data["status"]
                print(f"✅ Job status: {status}")
                
                if data.get("clips"):
                    print(f"🎬 Generated clips: {len(data['clips'])}")
                    for clip in data["clips"]:
                        print(f"   - {clip['event_type']} at {clip['start_time']:.1f}s")
                
                return True
            elif response.status_code == 404:
                print(f"❌ Job not found: {job_id}")
                return False
            else:
                print(f"❌ Status request failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ Status error: {e}")
            return False
    
    def test_list_jobs(self) -> bool:
        """Test list jobs endpoint."""
        print("📋 Testing list jobs endpoint...")
        
        try:
            response = self.session.get(f"{self.base_url}/jobs")
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ Found {len(data)} jobs")
                
                for job in data[:3]:  # Show first 3 jobs
                    print(f"   - {job['job_id']}: {job['status']}")
                
                return True
            else:
                print(f"❌ List jobs failed: {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ List jobs error: {e}")
            return False
    
    def run_full_test(self, test_file: str = "test_video.mp4") -> bool:
        """Run a complete API test."""
        print("🧪 Running full API test...\n")
        
        # Test health
        if not self.test_health():
            return False
        
        print()
        
        # Test upload
        job_id = self.test_upload(test_file)
        if not job_id:
            return False
        
        print()
        
        # Test status
        if not self.test_status(job_id):
            return False
        
        print()
        
        # Test list jobs
        if not self.test_list_jobs():
            return False
        
        print("\n✅ All API tests passed!")
        return True


def main():
    """Main test function."""
    print("🚀 ML Highlights API Test\n")
    
    # Check if API is running
    tester = APITester()
    
    try:
        response = requests.get("http://localhost:8000/", timeout=5)
        if response.status_code != 200:
            print("❌ API not responding. Make sure services are running.")
            print("Run: python start_services.py")
            return
    except requests.exceptions.RequestException:
        print("❌ Cannot connect to API at http://localhost:8000")
        print("Make sure services are running with: python start_services.py")
        return
    
    # Run tests
    success = tester.run_full_test()
    
    if success:
        print("\n🎉 API is working correctly!")
    else:
        print("\n❌ Some tests failed")


if __name__ == "__main__":
    main()
