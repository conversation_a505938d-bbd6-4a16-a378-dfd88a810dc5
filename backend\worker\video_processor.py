"""Main video processing pipeline that orchestrates audio/visual analysis and clip generation."""

import os
import tempfile
import async<PERSON>
from typing import List, Optional
from datetime import datetime
import structlog

from shared.models import (
    ProcessingJob, JobStatus, DetectedEvent, EventType,
    ProcessingConfig, EVENT_PRIORITY, MULTI_KILL_CHAINS, K<PERSON>L_SPREE_CHAINS
)
from shared.storage import get_storage_client
from worker.audio_detector import AudioEventDetector
from worker.visual_detector import VisualEventDetector
from worker.clip_generator import ClipGenerator

logger = structlog.get_logger()


class VideoProcessor:
    """Main video processing pipeline."""
    
    def __init__(self, assets_path: str = "assets", temp_path: str = None):
        self.assets_path = assets_path
        # Use system temp directory if not specified, ensure it's absolute
        if temp_path is None or temp_path == "/tmp":
            self.temp_path = tempfile.gettempdir()
        else:
            self.temp_path = temp_path

        # Ensure temp path exists and is absolute
        self.temp_path = os.path.abspath(self.temp_path)
        os.makedirs(self.temp_path, exist_ok=True)

        logger.info("VideoProcessor initialized",
                   assets_path=self.assets_path,
                   temp_path=self.temp_path)
        self.config = ProcessingConfig()
        
        # Initialize detectors
        self.audio_detector = AudioEventDetector(assets_path, self.config)
        self.visual_detector = VisualEventDetector(assets_path, self.config)
        self.clip_generator = ClipGenerator(self.config)
        
        # Storage client
        self.storage_client = get_storage_client()
    
    async def process_video(self, job: ProcessingJob, s3_key: str, fast_mode: bool = True) -> ProcessingJob:
        """Process a video file and generate highlight clips."""
        logger.info(
            "Starting video processing",
            job_id=job.job_id,
            s3_key=s3_key,
            filename=job.original_filename
        )
        
        start_time = datetime.utcnow()
        
        try:
            # Update job status
            job.status = JobStatus.PROCESSING
            job.started_at = start_time
            
            # Create temporary working directory
            logger.info("Creating temp directory", temp_path=self.temp_path)
            with tempfile.TemporaryDirectory(dir=self.temp_path) as temp_dir:
                logger.info("Temp directory created", temp_dir=temp_dir)
                # Download video from S3
                video_path = os.path.join(temp_dir, "input_video.mp4")
                success = await self.storage_client.download_file(s3_key, video_path)
                if not success:
                    raise Exception(f"Failed to download video from S3: {s3_key}")
                
                # Get video duration
                job.video_duration = self._get_video_duration(video_path)

                # Fast mode: limit processing to first 60 seconds for testing
                if fast_mode and job.video_duration > 60:
                    logger.info("Fast mode enabled: processing first 60 seconds only",
                               original_duration=job.video_duration)
                    # Create a shorter video clip for processing
                    short_video_path = os.path.join(temp_dir, "short_video.mp4")
                    self._create_short_clip(video_path, short_video_path, 60)
                    video_path = short_video_path
                    job.video_duration = 60

                # Audio detection (if enabled)
                if self.config.enable_audio_detection:
                    audio_path = os.path.join(temp_dir, "audio.wav")
                    if not self.audio_detector.extract_audio_from_video(video_path, audio_path):
                        logger.warning("Failed to extract audio, skipping audio detection")
                        audio_events = []
                    else:
                        # Detect audio events
                        logger.info("Starting audio event detection")
                        audio_events = self.audio_detector.detect_events(audio_path)
                else:
                    logger.info("Audio detection disabled, skipping")
                    audio_events = []
                
                # Detect visual events
                logger.info("Starting visual event detection")
                visual_events = self.visual_detector.detect_events(video_path)
                
                # Combine and rank events
                all_events = audio_events + visual_events

                # Detect chained events (multi-kills and kill sprees)
                chained_events = self._detect_chained_events(all_events)

                # Rank and filter events
                ranked_events = self._rank_and_filter_events(chained_events)
                
                job.detected_events = ranked_events
                
                logger.info(
                    "Event detection complete",
                    job_id=job.job_id,
                    audio_events=len(audio_events),
                    visual_events=len(visual_events),
                    total_events=len(all_events),
                    ranked_events=len(ranked_events)
                )
                
                # Generate clips
                if ranked_events:
                    clips = await self._generate_clips(
                        job, video_path, ranked_events, temp_dir
                    )
                    job.generated_clips = clips
                else:
                    logger.warning("No events detected, no clips generated")
                    job.generated_clips = []
                
                # Create playlist and zip
                if job.generated_clips:
                    await self._create_outputs(job, temp_dir)
                
                # Mark as completed
                job.status = JobStatus.COMPLETED
                job.completed_at = datetime.utcnow()
                job.processing_time = (job.completed_at - start_time).total_seconds()
                
                logger.info(
                    "Video processing completed",
                    job_id=job.job_id,
                    processing_time=job.processing_time,
                    clips_generated=len(job.generated_clips)
                )
        
        except Exception as e:
            logger.error(
                "Video processing failed",
                job_id=job.job_id,
                error=str(e)
            )
            job.status = JobStatus.FAILED
            job.error_message = str(e)
            job.completed_at = datetime.utcnow()
            if job.started_at:
                job.processing_time = (job.completed_at - job.started_at).total_seconds()
        
        return job

    def _create_short_clip(self, input_path: str, output_path: str, duration: int):
        """Create a short clip from the beginning of the video using FFmpeg."""
        try:
            import subprocess

            # Find ffmpeg executable
            ffmpeg_cmd = self._find_ffmpeg()
            if not ffmpeg_cmd:
                logger.error("FFmpeg not found, using original video")
                import shutil
                shutil.copy2(input_path, output_path)
                return

            cmd = [
                ffmpeg_cmd, "-i", input_path,
                "-t", str(duration),  # Duration in seconds
                "-c", "copy",  # Copy streams without re-encoding for speed
                "-y",  # Overwrite output file
                output_path
            ]
            subprocess.run(cmd, check=True, capture_output=True)
            logger.info("Created short clip", input_path=input_path, output_path=output_path, duration=duration)
        except Exception as e:
            logger.error("Failed to create short clip", error=str(e))
            # If FFmpeg fails, just use the original video
            import shutil
            shutil.copy2(input_path, output_path)

    def _find_ffmpeg(self) -> str:
        """Find FFmpeg executable in common locations."""
        import shutil
        import os

        # Try common names
        for name in ["ffmpeg", "ffmpeg.exe"]:
            # Check if in PATH
            if shutil.which(name):
                return name

        # Check common Windows installation paths
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
            # Winget installation path
            os.path.expanduser(r"~\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe")
        ]

        for path in common_paths:
            if os.path.exists(path):
                return path

        return None

    def _get_video_duration(self, video_path: str) -> Optional[float]:
        """Get video duration in seconds."""
        try:
            import cv2
            cap = cv2.VideoCapture(video_path)
            if cap.isOpened():
                fps = cap.get(cv2.CAP_PROP_FPS)
                frame_count = cap.get(cv2.CAP_PROP_FRAME_COUNT)
                duration = frame_count / fps if fps > 0 else None
                cap.release()
                return duration
            return None
        except Exception as e:
            logger.error("Failed to get video duration", video_path=video_path, error=str(e))
            return None

    def _rank_and_filter_events(self, events: List[DetectedEvent]) -> List[DetectedEvent]:
        """Rank and filter events by priority and confidence."""
        if not events:
            return events

        # Sort by priority (higher is better) then by confidence
        events.sort(key=lambda e: (EVENT_PRIORITY.get(e.event_type, 0), e.confidence), reverse=True)

        # Remove duplicates within merge window
        filtered_events = []
        for event in events:
            # Check if we already have a similar event nearby
            duplicate = False
            for existing in filtered_events:
                if (existing.event_type == event.event_type and
                    abs(existing.timestamp - event.timestamp) <= self.config.merge_window):
                    duplicate = True
                    break

            if not duplicate:
                filtered_events.append(event)

        # Limit number of clips per event type
        final_events = []
        event_counts = {}

        for event in filtered_events:
            count = event_counts.get(event.event_type, 0)
            if count < self.config.clip_config.max_clips_per_event:
                final_events.append(event)
                event_counts[event.event_type] = count + 1

        logger.info(
            "Ranked and filtered events",
            original_count=len(events),
            filtered_count=len(filtered_events),
            final_count=len(final_events),
            event_counts=event_counts
        )

        return final_events

    def _detect_chained_events(self, events: List[DetectedEvent]) -> List[DetectedEvent]:
        """Detect and enhance chained events (multi-kills and kill sprees)."""
        if not events:
            return events

        # Sort events by timestamp
        events.sort(key=lambda e: e.timestamp)

        enhanced_events = []
        processed_indices = set()

        for i, event in enumerate(events):
            if i in processed_indices:
                continue

            # Check if this event is part of a multi-kill chain
            if event.event_type in MULTI_KILL_CHAINS:
                chain_events = self._find_chain_events(
                    events, i, MULTI_KILL_CHAINS[event.event_type]
                )
                if chain_events:
                    # Create enhanced event with chain information
                    enhanced_event = self._create_chained_event(event, chain_events)
                    enhanced_events.append(enhanced_event)

                    # Mark chain events as processed
                    for chain_event in chain_events:
                        for j, e in enumerate(events):
                            if (e.timestamp == chain_event.timestamp and
                                e.event_type == chain_event.event_type):
                                processed_indices.add(j)
                                break
                    processed_indices.add(i)
                    continue

            # Check if this event is part of a kill spree chain
            if event.event_type in KILL_SPREE_CHAINS:
                chain_events = self._find_chain_events(
                    events, i, KILL_SPREE_CHAINS[event.event_type]
                )
                if chain_events:
                    # Create enhanced event with chain information
                    enhanced_event = self._create_chained_event(event, chain_events)
                    enhanced_events.append(enhanced_event)

                    # Mark chain events as processed
                    for chain_event in chain_events:
                        for j, e in enumerate(events):
                            if (e.timestamp == chain_event.timestamp and
                                e.event_type == chain_event.event_type):
                                processed_indices.add(j)
                                break
                    processed_indices.add(i)
                    continue

            # Regular event (not part of a chain)
            enhanced_events.append(event)
            processed_indices.add(i)

        logger.info(
            "Chained event detection complete",
            original_events=len(events),
            enhanced_events=len(enhanced_events),
            chains_detected=len(events) - len(enhanced_events)
        )

        return enhanced_events

    def _find_chain_events(
        self,
        events: List[DetectedEvent],
        main_event_index: int,
        required_chain: List[EventType]
    ) -> List[DetectedEvent]:
        """Find events that form a chain leading to the main event."""
        main_event = events[main_event_index]
        chain_events = []

        # Look backwards from main event within the chain window
        chain_window = self.config.clip_config.chain_window

        for required_event_type in required_chain:
            # Find the most recent occurrence of this event type before main event
            found_event = None

            for i in range(main_event_index - 1, -1, -1):
                event = events[i]

                # Check if event is within chain window
                if main_event.timestamp - event.timestamp > chain_window:
                    break

                # Check if this is the required event type
                if event.event_type == required_event_type:
                    found_event = event
                    break

            if found_event:
                chain_events.append(found_event)
            else:
                # Chain is broken if we can't find a required event
                return []

        return chain_events

    def _create_chained_event(
        self,
        main_event: DetectedEvent,
        chain_events: List[DetectedEvent]
    ) -> DetectedEvent:
        """Create an enhanced event that includes chain information."""

        # Calculate the start time for the clip (earliest event in chain)
        if chain_events:
            earliest_timestamp = min(e.timestamp for e in chain_events)
            # Use extra padding for chained events to capture the full sequence
            clip_start = earliest_timestamp - self.config.clip_config.chain_padding_before
        else:
            clip_start = main_event.timestamp - self.config.clip_config.padding_before

        # Create enhanced metadata
        enhanced_metadata = main_event.metadata.copy()
        enhanced_metadata.update({
            "is_chained": True,
            "chain_length": len(chain_events) + 1,
            "chain_events": [
                {
                    "event_type": e.event_type.value,
                    "timestamp": e.timestamp,
                    "confidence": e.confidence
                }
                for e in chain_events
            ],
            "clip_start_override": clip_start,
            "sequence_duration": main_event.timestamp - (chain_events[0].timestamp if chain_events else main_event.timestamp)
        })

        # Create enhanced event
        enhanced_event = DetectedEvent(
            event_type=main_event.event_type,
            timestamp=main_event.timestamp,
            confidence=main_event.confidence,
            source=main_event.source,
            metadata=enhanced_metadata
        )

        return enhanced_event

    async def _generate_clips(
        self,
        job: ProcessingJob,
        video_path: str,
        events: List[DetectedEvent],
        temp_dir: str
    ) -> List:
        """Generate video clips for detected events."""
        from ..shared.models import VideoClip

        clips = []
        clips_dir = os.path.join(temp_dir, "clips")
        os.makedirs(clips_dir, exist_ok=True)

        for i, event in enumerate(events):
            try:
                clip_id = f"{event.event_type.value}_{i}_{int(event.timestamp)}"
                clip_filename = f"{clip_id}.mp4"
                clip_path = os.path.join(clips_dir, clip_filename)

                # Generate clip
                success = await self.clip_generator.generate_clip(
                    video_path=video_path,
                    output_path=clip_path,
                    start_time=event.timestamp,
                    event_type=event.event_type,
                    event_metadata=event.metadata
                )

                if success and os.path.exists(clip_path):
                    # Upload to S3
                    s3_key = self.storage_client.get_clip_key(job.job_id, clip_id)
                    upload_success = await self.storage_client.upload_file(
                        clip_path, s3_key, "video/mp4"
                    )

                    if upload_success:
                        file_size = os.path.getsize(clip_path)

                        clip = VideoClip(
                            clip_id=clip_id,
                            event_type=event.event_type,
                            start_time=event.timestamp,
                            duration=self.config.clip_config.clip_duration,
                            file_path=s3_key,
                            file_size=file_size
                        )
                        clips.append(clip)

                        logger.info(
                            "Generated and uploaded clip",
                            job_id=job.job_id,
                            clip_id=clip_id,
                            event_type=event.event_type.value,
                            timestamp=event.timestamp,
                            s3_key=s3_key
                        )
                    else:
                        logger.error("Failed to upload clip", clip_id=clip_id, s3_key=s3_key)
                else:
                    logger.error("Failed to generate clip", clip_id=clip_id, event_type=event.event_type.value)

            except Exception as e:
                logger.error(
                    "Clip generation failed",
                    job_id=job.job_id,
                    event_type=event.event_type.value,
                    timestamp=event.timestamp,
                    error=str(e)
                )

        return clips

    async def _create_outputs(self, job: ProcessingJob, temp_dir: str):
        """Create playlist JSON and zip file with all clips."""
        import json
        import zipfile

        try:
            # Create playlist JSON
            playlist_data = {
                "job_id": job.job_id,
                "original_filename": job.original_filename,
                "video_duration": job.video_duration,
                "processing_time": job.processing_time,
                "created_at": job.created_at.isoformat() if job.created_at else None,
                "clips": [
                    {
                        "clip_id": clip.clip_id,
                        "event_type": clip.event_type.value,
                        "start_time": clip.start_time,
                        "duration": clip.duration,
                        "file_size": clip.file_size,
                        "download_url": f"clips/{clip.clip_id}.mp4"  # Relative path in zip
                    }
                    for clip in job.generated_clips
                ]
            }

            playlist_path = os.path.join(temp_dir, "playlist.json")
            with open(playlist_path, 'w') as f:
                json.dump(playlist_data, f, indent=2)

            # Upload playlist
            playlist_s3_key = self.storage_client.get_playlist_key(job.job_id)
            await self.storage_client.upload_file(
                playlist_path, playlist_s3_key, "application/json"
            )
            job.playlist_url = await self.storage_client.generate_presigned_download_url(
                playlist_s3_key, expires_in=86400  # 24 hours
            )

            # Create zip file with all clips
            zip_path = os.path.join(temp_dir, "highlights.zip")
            with zipfile.ZipFile(zip_path, 'w', zipfile.ZIP_DEFLATED) as zipf:
                # Add playlist
                zipf.write(playlist_path, "playlist.json")

                # Download and add each clip
                clips_dir = os.path.join(temp_dir, "clips_for_zip")
                os.makedirs(clips_dir, exist_ok=True)

                for clip in job.generated_clips:
                    clip_local_path = os.path.join(clips_dir, f"{clip.clip_id}.mp4")

                    # Download clip from S3
                    if await self.storage_client.download_file(clip.file_path, clip_local_path):
                        # Add to zip
                        zipf.write(clip_local_path, f"clips/{clip.clip_id}.mp4")
                    else:
                        logger.error("Failed to download clip for zip", clip_id=clip.clip_id)

            # Upload zip file
            zip_s3_key = self.storage_client.get_zip_key(job.job_id)
            await self.storage_client.upload_file(
                zip_path, zip_s3_key, "application/zip"
            )
            job.zip_url = await self.storage_client.generate_presigned_download_url(
                zip_s3_key, expires_in=86400  # 24 hours
            )

            logger.info(
                "Created outputs",
                job_id=job.job_id,
                playlist_url=job.playlist_url,
                zip_url=job.zip_url,
                clips_count=len(job.generated_clips)
            )

        except Exception as e:
            logger.error(
                "Failed to create outputs",
                job_id=job.job_id,
                error=str(e)
            )
