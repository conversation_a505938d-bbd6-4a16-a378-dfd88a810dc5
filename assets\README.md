# Assets Directory

This directory contains reference audio and visual templates for Mobile Legends event detection.

## Audio References (`audio/`)

Place WAV files for each event type:

**Kill Streak Events:**
- `first_blood.wav` - First blood announcer audio
- `double_kill.wav` - Double kill announcer audio
- `triple_kill.wav` - Triple kill announcer audio
- `mega_kill.wav` - Mega kill announcer audio
- `maniac.wav` - Maniac announcer audio
- `savage.wav` - Savage announcer audio

**Kill Spree Events:**
- `killing_spree.wav` - Killing spree announcer audio
- `monster_kill.wav` - Monster kill announcer audio
- `godlike.wav` - Godlike announcer audio
- `legendary.wav` - Legendary announcer audio
- `unstoppable.wav` - Unstoppable announcer audio

**General Events:**
- `has_slain.wav` - Has slain announcer audio

**Audio Requirements:**
- Format: WAV, 22050 Hz sample rate, mono
- Duration: 1-3 seconds of clean announcer voice
- No background music or game sounds

## Visual Templates (`templates/`)

Place PNG files for each visual event:

**Kill Streak Events (Kill Feed):**
- `first_blood_feed.png` - Kill feed notification for first blood
- `double_kill_feed.png` - Kill feed notification for double kill
- `triple_kill_feed.png` - Kill feed notification for triple kill
- `mega_kill_feed.png` - Kill feed notification for mega kill
- `maniac_feed.png` - Kill feed notification for maniac
- `savage_feed.png` - Kill feed notification for savage

**Kill Spree Events (Kill Feed):**
- `killing_spree_feed.png` - Kill feed notification for killing spree
- `monster_kill_feed.png` - Kill feed notification for monster kill
- `godlike_feed.png` - Kill feed notification for godlike
- `legendary_feed.png` - Kill feed notification for legendary
- `unstoppable_feed.png` - Kill feed notification for unstoppable

**General Events:**
- `has_slain_feed.png` - Kill feed notification for has slain

**Objective Events:**
- `lord_banner.png` - Lord kill banner/notification
- `turtle_banner.png` - Turtle kill banner/notification
- `crystal_burst.png` - Crystal destruction effect
- `tower_destroy.png` - Tower destruction notification

**Image Requirements:**
- Format: PNG with transparency if needed
- Size: Cropped to just the relevant UI element
- Quality: Clear, high-contrast images
- Multiple resolutions recommended for better matching

## Creating Reference Assets

### Audio References
1. Record or extract clean announcer audio from Mobile Legends
2. Use audio editing software to isolate just the announcer voice
3. Export as WAV, 22050 Hz, mono
4. Keep duration between 1-3 seconds

### Visual Templates  
1. Take screenshots during gameplay when events occur
2. Crop to just the relevant UI element (kill feed, banner, etc.)
3. Save as PNG
4. Test with different screen resolutions/UI scales

## Testing Assets

Use the provided test scripts to validate your reference assets:

```bash
# Test audio detection
python -m tests.test_audio_detection assets/audio/double_kill.wav

# Test visual detection  
python -m tests.test_visual_detection assets/templates/double_kill_feed.png
```
