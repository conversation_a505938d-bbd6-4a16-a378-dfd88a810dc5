#!/usr/bin/env python3
"""
Test Whisper-based audio detection on sample audio files.
"""

import sys
import os
import tempfile

# Add backend to path
sys.path.append('backend')

def test_whisper_audio():
    """Test Whisper audio detection on sample audio files."""
    print("🎤 Testing Whisper-Based Audio Detection")
    print("=" * 60)
    
    try:
        from worker.whisper_audio_detector import WhisperAudioDetector
        from shared.models import ProcessingConfig, EventType
        import librosa
        import soundfile as sf
        import numpy as np
        
        # Audio samples directory
        audio_dir = "assets/audio"
        
        if not os.path.exists(audio_dir):
            print(f"❌ Audio directory not found: {audio_dir}")
            return False
        
        print(f"✅ Audio directory found: {audio_dir}")
        
        # List all audio files
        audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
        print(f"📚 Found {len(audio_files)} audio samples:")
        for audio_file in audio_files:
            print(f"  🎵 {audio_file}")
        
        # Create Whisper audio detector
        config = ProcessingConfig()
        print(f"\n🔧 Whisper settings:")
        print(f"   🎤 Model: tiny.en (quantized)")
        print(f"   🔊 Sample rate: 16kHz")
        print(f"   🎯 VAD filtering: enabled")
        print(f"   📊 Probability threshold: 0.45")
        
        detector = WhisperAudioDetector(config)
        print(f"✅ Whisper detector created")
        print(f"📚 Hotwords loaded: {len(detector.all_hotwords)}")
        print(f"🎯 Event types: {len(detector.hotwords)}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"\n🧪 Testing each sample audio file...")
            
            results = {}
            
            for audio_file in audio_files[:3]:  # Test first 3 files for speed
                audio_path = os.path.join(audio_dir, audio_file)
                event_name = audio_file.replace('.wav', '')
                
                print(f"\n🎵 Testing: {audio_file}")
                print(f"   📁 Path: {audio_path}")
                
                # Load audio info
                try:
                    audio_data, sr = librosa.load(audio_path, sr=None)
                    duration = len(audio_data) / sr
                    print(f"   ⏱️ Duration: {duration:.2f}s")
                    print(f"   📊 Sample rate: {sr} Hz")
                    
                    # Create a test audio file that includes this sample
                    # Add some silence before and after to simulate real game audio
                    silence_before = np.zeros(int(1 * sr))  # 1 second silence
                    silence_after = np.zeros(int(1 * sr))   # 1 second silence
                    
                    test_audio = np.concatenate([silence_before, audio_data, silence_after])
                    test_duration = len(test_audio) / sr
                    
                    # Convert to 16kHz for Whisper
                    test_audio_16k = librosa.resample(test_audio, orig_sr=sr, target_sr=16000)
                    
                    test_audio_path = os.path.join(temp_dir, f"test_{audio_file}")
                    sf.write(test_audio_path, test_audio_16k, 16000)
                    
                    print(f"   🧪 Test audio created: {test_duration:.2f}s total (16kHz)")
                    
                    # Run Whisper detection
                    print(f"   🎤 Running Whisper transcription...")
                    events = detector.detect_events(test_audio_path)
                    
                    print(f"   📊 Events detected: {len(events)}")
                    
                    # Analyze results
                    correct_detections = []
                    incorrect_detections = []
                    
                    for event in events:
                        # Check if detected event matches the expected event
                        expected_event = event_name.upper()
                        detected_event = event.event_type.value.upper()
                        
                        if expected_event == detected_event:
                            correct_detections.append(event)
                            print(f"   ✅ CORRECT: {event.event_type.value} at {event.timestamp:.1f}s (confidence: {event.confidence:.3f})")
                            if hasattr(event, 'metadata') and 'word' in event.metadata:
                                print(f"      🎤 Word: '{event.metadata['word']}'")
                        else:
                            incorrect_detections.append(event)
                            print(f"   ❌ WRONG: {event.event_type.value} at {event.timestamp:.1f}s (expected: {event_name})")
                            if hasattr(event, 'metadata') and 'word' in event.metadata:
                                print(f"      🎤 Word: '{event.metadata['word']}'")
                    
                    # Store results
                    results[event_name] = {
                        'total_detected': len(events),
                        'correct': len(correct_detections),
                        'incorrect': len(incorrect_detections),
                        'expected_time': 1.0,  # Should detect around 1 second (after silence)
                        'actual_detections': events
                    }
                    
                    if len(correct_detections) > 0:
                        print(f"   🎯 SUCCESS: Correctly detected {event_name}")
                    elif len(events) == 0:
                        print(f"   ⚠️ MISSED: No events detected for {event_name}")
                    else:
                        print(f"   ❌ FAILED: Wrong events detected for {event_name}")
                
                except Exception as e:
                    print(f"   ❌ ERROR: {e}")
                    results[event_name] = {'error': str(e)}
            
            # Summary
            print(f"\n📊 WHISPER DETECTION SUMMARY")
            print(f"=" * 50)
            
            total_files = len(results)
            successful_detections = 0
            missed_detections = 0
            false_positives = 0
            
            for event_name, result in results.items():
                if 'error' in result:
                    print(f"❌ {event_name}: ERROR - {result['error']}")
                else:
                    correct = result['correct']
                    incorrect = result['incorrect']
                    total = result['total_detected']
                    
                    if correct > 0:
                        successful_detections += 1
                        print(f"✅ {event_name}: DETECTED ({correct} correct, {incorrect} wrong)")
                    else:
                        missed_detections += 1
                        if total > 0:
                            print(f"❌ {event_name}: MISSED (detected {total} wrong events)")
                        else:
                            print(f"⚠️ {event_name}: MISSED (no detection)")
                    
                    false_positives += incorrect
            
            print(f"\n🎯 WHISPER RESULTS:")
            print(f"   📚 Total samples tested: {total_files}")
            print(f"   ✅ Successful detections: {successful_detections}")
            print(f"   ⚠️ Missed detections: {missed_detections}")
            print(f"   ❌ False positives: {false_positives}")
            print(f"   📊 Success rate: {successful_detections/total_files*100:.1f}%")
            
            if successful_detections >= total_files * 0.8:  # 80% success rate
                print(f"\n🎉 EXCELLENT: Whisper audio detection is working well!")
                return True
            elif successful_detections >= total_files * 0.5:  # 50% success rate
                print(f"\n✅ GOOD: Whisper audio detection is working but needs tuning")
                return True
            else:
                print(f"\n❌ POOR: Whisper audio detection needs improvement")
                return False
        
    except Exception as e:
        print(f"❌ Whisper audio test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    success = test_whisper_audio()
    if success:
        print("\n🎉 Whisper audio test completed successfully!")
        print("🚀 Ready for best-in-class audio detection!")
    else:
        print("\n❌ Whisper audio test failed!")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
