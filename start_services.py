#!/usr/bin/env python3
"""Simple script to start the ML Highlights services for development."""

import os
import sys
import subprocess
import time
import signal
from typing import List, Optional

class ServiceManager:
    """Manages starting and stopping services."""
    
    def __init__(self):
        self.processes: List[subprocess.Popen] = []
        self.running = True
    
    def start_redis(self) -> bool:
        """Start Redis using Docker."""
        print("🔴 Starting Redis...")
        try:
            # Check if Redis is already running
            result = subprocess.run(
                ["docker", "ps", "--filter", "name=redis", "--format", "{{.Names}}"],
                capture_output=True, text=True
            )
            
            if "redis" in result.stdout:
                print("✅ Redis already running")
                return True
            
            # Start Redis container
            cmd = [
                "docker", "run", "-d",
                "--name", "ml-highlights-redis",
                "-p", "6379:6379",
                "redis:7-alpine"
            ]
            
            result = subprocess.run(cmd, capture_output=True, text=True)
            if result.returncode == 0:
                print("✅ Redis started")
                time.sleep(2)  # Give Redis time to start
                return True
            else:
                print(f"❌ Failed to start Redis: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ Error starting Redis: {e}")
            return False
    
    def start_gateway(self) -> Optional[subprocess.Popen]:
        """Start the FastAPI gateway."""
        print("🌐 Starting API Gateway...")
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env.update({
                "REDIS_URL": "redis://localhost:6379",
                "STORAGE_PROVIDER": "backblaze",
                "S3_BUCKET": "ml-highlights-dev",
                "S3_ACCESS_KEY_ID": "dev",
                "S3_SECRET_ACCESS_KEY": "dev",
                "S3_REGION": "us-west-004",
                "S3_ENDPOINT_URL": "https://s3.us-west-004.backblazeb2.com",
                "DEBUG": "true"
            })
            
            cmd = [
                sys.executable, "-m", "uvicorn",
                "backend.gateway.main:app",
                "--host", "0.0.0.0",
                "--port", "8000",
                "--reload"
            ]
            
            process = subprocess.Popen(cmd, env=env)
            self.processes.append(process)
            
            print("✅ API Gateway started on http://localhost:8000")
            return process
            
        except Exception as e:
            print(f"❌ Error starting gateway: {e}")
            return None
    
    def start_worker(self) -> Optional[subprocess.Popen]:
        """Start the video processing worker."""
        print("⚙️ Starting Worker...")
        
        try:
            # Set environment variables
            env = os.environ.copy()
            env.update({
                "REDIS_URL": "redis://localhost:6379",
                "STORAGE_PROVIDER": "backblaze",
                "S3_BUCKET": "ml-highlights-dev",
                "S3_ACCESS_KEY_ID": "dev",
                "S3_SECRET_ACCESS_KEY": "dev",
                "S3_REGION": "us-west-004",
                "S3_ENDPOINT_URL": "https://s3.us-west-004.backblazeb2.com",
                "ASSETS_PATH": "assets",
                "TEMP_PATH": "temp"
            })
            
            # Create temp directory
            os.makedirs("temp", exist_ok=True)
            
            cmd = [sys.executable, "-m", "backend.worker.main"]
            
            process = subprocess.Popen(cmd, env=env)
            self.processes.append(process)
            
            print("✅ Worker started")
            return process
            
        except Exception as e:
            print(f"❌ Error starting worker: {e}")
            return None
    
    def check_dependencies(self) -> bool:
        """Check if required dependencies are available."""
        print("🔍 Checking dependencies...")
        
        # Check Docker
        try:
            subprocess.run(["docker", "--version"], capture_output=True, check=True)
            print("✅ Docker available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ Docker not found. Please install Docker.")
            return False
        
        # Check FFmpeg
        try:
            subprocess.run(["ffmpeg", "-version"], capture_output=True, check=True)
            print("✅ FFmpeg available")
        except (subprocess.CalledProcessError, FileNotFoundError):
            print("❌ FFmpeg not found. Please install FFmpeg.")
            return False
        
        # Check Python packages
        required_packages = ["fastapi", "uvicorn", "redis", "opencv-python", "librosa"]
        missing = []
        
        for package in required_packages:
            try:
                __import__(package.replace("-", "_"))
                print(f"✅ {package}")
            except ImportError:
                missing.append(package)
                print(f"❌ {package}")
        
        if missing:
            print(f"\n❌ Missing packages. Install with:")
            print(f"pip install {' '.join(missing)}")
            return False
        
        return True
    
    def setup_signal_handlers(self):
        """Set up signal handlers for graceful shutdown."""
        def signal_handler(signum, frame):
            print(f"\n🛑 Received signal {signum}, shutting down...")
            self.shutdown()
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        signal.signal(signal.SIGTERM, signal_handler)
    
    def shutdown(self):
        """Shutdown all services."""
        print("\n🛑 Shutting down services...")
        
        # Stop processes
        for process in self.processes:
            try:
                process.terminate()
                process.wait(timeout=5)
                print("✅ Process stopped")
            except subprocess.TimeoutExpired:
                process.kill()
                print("⚠️ Process killed (timeout)")
            except Exception as e:
                print(f"❌ Error stopping process: {e}")
        
        # Stop Redis container
        try:
            subprocess.run(
                ["docker", "stop", "ml-highlights-redis"],
                capture_output=True, timeout=10
            )
            subprocess.run(
                ["docker", "rm", "ml-highlights-redis"],
                capture_output=True, timeout=10
            )
            print("✅ Redis stopped")
        except Exception as e:
            print(f"⚠️ Error stopping Redis: {e}")
        
        self.running = False
    
    def start_all(self):
        """Start all services."""
        print("🚀 Starting ML Highlights Services\n")
        
        # Check dependencies
        if not self.check_dependencies():
            return False
        
        # Setup signal handlers
        self.setup_signal_handlers()
        
        # Start services
        if not self.start_redis():
            return False
        
        gateway_process = self.start_gateway()
        if not gateway_process:
            return False
        
        worker_process = self.start_worker()
        if not worker_process:
            return False
        
        print("\n🎉 All services started successfully!")
        print("\n📋 Service URLs:")
        print("   API Gateway: http://localhost:8000")
        print("   API Docs: http://localhost:8000/docs")
        print("   Health Check: http://localhost:8000/health")
        
        print("\n📝 Next steps:")
        print("   1. Place reference assets in assets/ directory")
        print("   2. Configure AWS S3 credentials in .env")
        print("   3. Upload a Mobile Legends video via API")
        
        # Wait for processes
        try:
            while self.running:
                time.sleep(1)
                
                # Check if any process died
                for process in self.processes:
                    if process.poll() is not None:
                        print(f"⚠️ Process {process.pid} exited")
                        self.running = False
                        break
        except KeyboardInterrupt:
            pass
        
        self.shutdown()
        return True


def main():
    """Main entry point."""
    manager = ServiceManager()
    success = manager.start_all()
    
    if not success:
        print("\n❌ Failed to start services")
        sys.exit(1)


if __name__ == "__main__":
    main()
