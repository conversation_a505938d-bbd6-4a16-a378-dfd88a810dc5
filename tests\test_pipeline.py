"""Test script for the complete video processing pipeline."""

import asyncio
import os
import tempfile
from datetime import datetime
import sys

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backend'))

from shared.models import ProcessingJob, JobStatus, ProcessingConfig
from worker.video_processor import VideoProcessor


async def test_pipeline():
    """Test the complete video processing pipeline."""
    
    # Check if test video exists
    test_video = "test_video.mp4"
    if not os.path.exists(test_video):
        print(f"❌ Test video '{test_video}' not found")
        print("Please place a Mobile Legends match recording as 'test_video.mp4'")
        return False
    
    print(f"✅ Found test video: {test_video}")
    
    # Create test job
    job = ProcessingJob(
        job_id="test-job-001",
        status=JobStatus.QUEUED,
        original_filename="test_video.mp4",
        file_size=os.path.getsize(test_video),
        created_at=datetime.utcnow()
    )
    
    print(f"📝 Created test job: {job.job_id}")
    
    # Initialize processor
    processor = VideoProcessor(
        assets_path="assets",
        temp_path=tempfile.gettempdir()
    )
    
    print("🔧 Initialized video processor")
    
    # Test audio detector
    print("\n🎵 Testing audio detection...")
    audio_path = "test_audio.wav"
    
    # Extract audio first
    if processor.audio_detector.extract_audio_from_video(test_video, audio_path):
        print("✅ Audio extraction successful")
        
        # Test audio detection
        audio_events = processor.audio_detector.detect_events(audio_path)
        print(f"🎯 Detected {len(audio_events)} audio events")
        
        for event in audio_events:
            print(f"   - {event.event_type.value} at {event.timestamp:.1f}s (confidence: {event.confidence:.3f})")
    else:
        print("❌ Audio extraction failed")
    
    # Test visual detector
    print("\n👁️ Testing visual detection...")
    visual_events = processor.visual_detector.detect_events(test_video)
    print(f"🎯 Detected {len(visual_events)} visual events")
    
    for event in visual_events:
        print(f"   - {event.event_type.value} at {event.timestamp:.1f}s (confidence: {event.confidence:.3f})")
    
    # Test clip generation
    print("\n🎬 Testing clip generation...")
    all_events = audio_events + visual_events
    
    if all_events:
        # Take first event for testing
        test_event = all_events[0]
        
        clip_path = f"test_clip_{test_event.event_type.value}.mp4"
        success = await processor.clip_generator.generate_clip(
            video_path=test_video,
            output_path=clip_path,
            start_time=test_event.timestamp,
            event_type=test_event.event_type
        )
        
        if success and os.path.exists(clip_path):
            print(f"✅ Generated test clip: {clip_path}")
            print(f"   Size: {os.path.getsize(clip_path)} bytes")
        else:
            print("❌ Clip generation failed")
    else:
        print("⚠️ No events detected, skipping clip generation test")
    
    print("\n📊 Pipeline test completed!")
    return True


def check_dependencies():
    """Check if required dependencies are available."""
    print("🔍 Checking dependencies...")
    
    # Check FFmpeg
    import subprocess
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            print("✅ FFmpeg available")
        else:
            print("❌ FFmpeg not found")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found in PATH")
        return False
    
    # Check Python packages
    required_packages = [
        "librosa", "opencv-python", "numpy", "scipy", 
        "scikit-learn", "soundfile", "structlog"
    ]
    
    missing_packages = []
    for package in required_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"✅ {package}")
        except ImportError:
            print(f"❌ {package}")
            missing_packages.append(package)
    
    if missing_packages:
        print(f"\n❌ Missing packages: {', '.join(missing_packages)}")
        print("Install with: pip install " + " ".join(missing_packages))
        return False
    
    return True


def check_assets():
    """Check if reference assets are available."""
    print("\n📁 Checking reference assets...")
    
    audio_dir = "assets/audio"
    templates_dir = "assets/templates"
    
    # Check directories exist
    if not os.path.exists(audio_dir):
        print(f"❌ Audio directory not found: {audio_dir}")
        return False
    
    if not os.path.exists(templates_dir):
        print(f"❌ Templates directory not found: {templates_dir}")
        return False
    
    # Check for audio files
    audio_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
    print(f"🎵 Found {len(audio_files)} audio reference files")

    # List expected audio files
    expected_audio = [
        "first_blood.wav", "double_kill.wav", "triple_kill.wav", "mega_kill.wav",
        "maniac.wav", "savage.wav", "killing_spree.wav", "monster_kill.wav",
        "godlike.wav", "legendary.wav", "unstoppable.wav", "has_slain.wav"
    ]

    missing_audio = [f for f in expected_audio if f not in audio_files]
    if missing_audio:
        print(f"⚠️ Missing audio files: {', '.join(missing_audio)}")
    else:
        print("✅ All expected audio files present")

    # Check for template files
    template_files = [f for f in os.listdir(templates_dir) if f.endswith('.png')]
    print(f"🖼️ Found {len(template_files)} template files")
    
    if len(audio_files) == 0 and len(template_files) == 0:
        print("⚠️ No reference assets found. Detection may not work.")
        print("See assets/README.md for instructions on creating reference assets.")
    
    return True


async def main():
    """Main test function."""
    print("🚀 ML Highlights Pipeline Test\n")
    
    # Check dependencies
    if not check_dependencies():
        print("\n❌ Dependency check failed")
        return
    
    # Check assets
    if not check_assets():
        print("\n❌ Assets check failed")
        return
    
    # Run pipeline test
    print("\n🧪 Running pipeline test...")
    await test_pipeline()


if __name__ == "__main__":
    asyncio.run(main())
