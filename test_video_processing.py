#!/usr/bin/env python3
"""
Test video processing directly to verify temp directory fix.
"""

import sys
import os
import tempfile
from datetime import datetime

# Add backend to path
sys.path.append('backend')

def test_temp_directory():
    """Test temp directory creation directly."""
    print("🧪 Testing temp directory handling...")
    
    try:
        from worker.video_processor import VideoProcessor
        
        # Create processor
        processor = VideoProcessor()
        print(f"✅ VideoProcessor created")
        print(f"📁 Temp path: {processor.temp_path}")
        
        # Test temp directory creation (same as in process_video)
        with tempfile.TemporaryDirectory(dir=processor.temp_path) as temp_dir:
            print(f"✅ Temp directory created: {temp_dir}")
            
            # Test file operations
            test_file = os.path.join(temp_dir, "test_video.mp4")
            with open(test_file, 'w') as f:
                f.write("test video content")
            print(f"✅ Test file created: {test_file}")
            
            # Test subdirectory creation
            clips_dir = os.path.join(temp_dir, "clips")
            os.makedirs(clips_dir, exist_ok=True)
            print(f"✅ Clips directory created: {clips_dir}")
            
        print("✅ Temp directory handling works correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Temp directory test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_job_processing():
    """Test job processing structure."""
    print("\n🧪 Testing job processing structure...")
    
    try:
        from shared.models import ProcessingJob, JobStatus
        
        # Create a test job
        job = ProcessingJob(
            job_id="test-job-123",
            status=JobStatus.QUEUED,
            original_filename="test_video.mp4",
            file_size=1000000,
            created_at=datetime.utcnow()
        )
        
        print(f"✅ Test job created: {job.job_id}")
        print(f"📊 Status: {job.status}")
        print(f"📁 Filename: {job.original_filename}")
        
        return True
        
    except Exception as e:
        print(f"❌ Job processing test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main test function."""
    print("🚀 Testing Video Processing Components")
    print("=" * 60)
    
    # Test temp directory
    if not test_temp_directory():
        return 1
    
    # Test job processing
    if not test_job_processing():
        return 1
    
    print("\n🎉 All video processing tests passed!")
    print("✅ Worker should be able to process videos successfully")
    return 0

if __name__ == "__main__":
    sys.exit(main())
