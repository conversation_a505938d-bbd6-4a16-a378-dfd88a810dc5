#!/usr/bin/env python3
"""
Test script to verify worker functionality.
"""

import sys
import os
import tempfile

# Add backend to path
sys.path.append('backend')

def test_video_processor():
    """Test VideoProcessor initialization."""
    print("🧪 Testing VideoProcessor...")
    
    try:
        from worker.video_processor import VideoProcessor
        
        # Test default initialization
        processor = VideoProcessor()
        print(f"✅ VideoProcessor initialized")
        print(f"📂 Assets path: {processor.assets_path}")
        print(f"📁 Temp path: {processor.temp_path}")
        
        # Test temp directory creation
        with tempfile.TemporaryDirectory(dir=processor.temp_path) as temp_dir:
            print(f"✅ Temp directory created: {temp_dir}")
            
            # Test file creation in temp dir
            test_file = os.path.join(temp_dir, "test.txt")
            with open(test_file, 'w') as f:
                f.write("test")
            print(f"✅ File created in temp dir: {test_file}")
        
        print("✅ VideoProcessor temp directory handling works!")
        return True
        
    except Exception as e:
        print(f"❌ VideoProcessor test failed: {e}")
        return False

def test_imports():
    """Test all worker imports."""
    print("🧪 Testing worker imports...")
    
    try:
        from worker.main import Worker
        print("✅ Worker main imported")
        
        from worker.video_processor import VideoProcessor
        print("✅ VideoProcessor imported")
        
        from worker.audio_detector import AudioEventDetector
        print("✅ AudioEventDetector imported")
        
        from worker.visual_detector import VisualEventDetector
        print("✅ VisualEventDetector imported")
        
        from worker.clip_generator import ClipGenerator
        print("✅ ClipGenerator imported")
        
        print("✅ All worker imports successful!")
        return True
        
    except Exception as e:
        print(f"❌ Import test failed: {e}")
        return False

def main():
    """Main test function."""
    print("🚀 Testing Worker Components")
    print("=" * 50)
    
    # Test imports
    if not test_imports():
        return 1
    
    print()
    
    # Test VideoProcessor
    if not test_video_processor():
        return 1
    
    print()
    print("🎉 All tests passed! Worker should be ready.")
    return 0

if __name__ == "__main__":
    sys.exit(main())
