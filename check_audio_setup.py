#!/usr/bin/env python3
"""Simple check for audio setup without dependencies."""

import os
from enum import Enum


class EventType(str, Enum):
    """Types of game events that can be detected."""
    # Kill streak events (audio)
    FIRST_BLOOD = "first_blood"
    DOUBLE_KILL = "double_kill"
    TRIPLE_KILL = "triple_kill"
    MEGA_KILL = "mega_kill"
    MANIAC = "maniac"
    SAVAGE = "savage"
    
    # Kill spree events (audio)
    KILLING_SPREE = "killing_spree"
    MONSTER_KILL = "monster_kill"
    GODLIKE = "godlike"
    LEGENDARY = "legendary"
    UNSTOPPABLE = "unstoppable"
    
    # General kill event (audio)
    HAS_SLAIN = "has_slain"


def check_audio_files():
    """Check the audio files in the assets directory."""
    print("🎵 Checking Audio Files Setup\n")
    
    audio_dir = "assets/audio"
    if not os.path.exists(audio_dir):
        print(f"❌ Audio directory not found: {audio_dir}")
        return False
    
    # Expected files based on your actual files
    expected_files = {
        EventType.FIRST_BLOOD: "first_blood.wav",
        EventType.DOUBLE_KILL: "double_kill.wav", 
        EventType.TRIPLE_KILL: "triple_kill.wav",
        EventType.MEGA_KILL: "mega_kill.wav",
        EventType.MANIAC: "maniac.wav",
        EventType.SAVAGE: "savage.wav",
        EventType.KILLING_SPREE: "killing_spree.wav",
        EventType.MONSTER_KILL: "monster_kill.wav",
        EventType.GODLIKE: "godlike.wav",
        EventType.LEGENDARY: "legendary.wav",
        EventType.UNSTOPPABLE: "unstoppable.wav",
        EventType.HAS_SLAIN: "has_slain.wav",
    }
    
    print("📂 Expected Audio Files:")
    found_count = 0
    missing_count = 0
    
    for event_type, filename in expected_files.items():
        file_path = os.path.join(audio_dir, filename)
        if os.path.exists(file_path):
            file_size = os.path.getsize(file_path)
            duration_est = file_size / 44100  # Rough estimate
            print(f"   ✅ {filename:<20} ({file_size:>8,} bytes, ~{duration_est:.1f}s)")
            found_count += 1
        else:
            print(f"   ❌ {filename:<20} (missing)")
            missing_count += 1
    
    # Check for extra files
    actual_files = [f for f in os.listdir(audio_dir) if f.endswith('.wav')]
    expected_filenames = list(expected_files.values())
    extra_files = [f for f in actual_files if f not in expected_filenames]
    
    if extra_files:
        print(f"\n⚠️ Extra Files Found:")
        for filename in extra_files:
            file_path = os.path.join(audio_dir, filename)
            file_size = os.path.getsize(file_path)
            print(f"   ⚠️ {filename:<20} ({file_size:>8,} bytes)")
    
    print(f"\n📊 Summary:")
    print(f"   ✅ Found: {found_count}/11 expected files")
    print(f"   ❌ Missing: {missing_count} files")
    print(f"   ⚠️ Extra: {len(extra_files)} files")
    
    return missing_count == 0


def check_event_priorities():
    """Check the event priority configuration."""
    print("\n🏆 Event Priority Configuration\n")
    
    # Event priority for ranking when multiple events are detected
    EVENT_PRIORITY = {
        # Highest priority - rare multi-kills
        EventType.SAVAGE: 15,
        EventType.MANIAC: 14,
        EventType.MEGA_KILL: 13,
        EventType.TRIPLE_KILL: 12,
        EventType.DOUBLE_KILL: 11,
        
        # High priority - kill sprees
        EventType.UNSTOPPABLE: 10,
        EventType.LEGENDARY: 9,
        EventType.GODLIKE: 8,
        EventType.MONSTER_KILL: 7,
        EventType.KILLING_SPREE: 6,
        
        # Medium priority - special events
        EventType.FIRST_BLOOD: 5,
        
        # Lowest priority - common events
        EventType.HAS_SLAIN: 0,
    }
    
    # Sort events by priority
    sorted_events = sorted(EVENT_PRIORITY.items(), key=lambda x: x[1], reverse=True)
    
    print("Priority Ranking (highest to lowest):")
    for i, (event, priority) in enumerate(sorted_events, 1):
        print(f"   {i:2d}. {event.value:<15} (priority: {priority:2d})")
    
    # Check for missing events
    all_audio_events = list(EventType)
    missing_priorities = [e for e in all_audio_events if e not in EVENT_PRIORITY]
    
    if missing_priorities:
        print(f"\n❌ Events missing from priority list:")
        for event in missing_priorities:
            print(f"   ❌ {event.value}")
        return False
    else:
        print(f"\n✅ All {len(all_audio_events)} audio events have priorities assigned")
        return True


def check_configuration():
    """Check the application configuration."""
    print("\n⚙️ Configuration Check\n")
    
    # Check if models file has been updated
    models_file = "backend/shared/models.py"
    if os.path.exists(models_file):
        with open(models_file, 'r') as f:
            content = f.read()
            
        # Check for new event types
        new_events = ["MEGA_KILL", "GODLIKE", "LEGENDARY", "UNSTOPPABLE", "HAS_SLAIN"]
        found_events = [event for event in new_events if event in content]
        
        print(f"✅ Models file exists: {models_file}")
        print(f"✅ New events in models: {len(found_events)}/5")
        
        if len(found_events) == 5:
            print("✅ All new event types are configured")
            return True
        else:
            missing = [e for e in new_events if e not in found_events]
            print(f"❌ Missing event types: {missing}")
            return False
    else:
        print(f"❌ Models file not found: {models_file}")
        return False


def main():
    """Main check function."""
    print("🚀 ML Highlights Audio Setup Check\n")
    
    # Check 1: Audio files
    files_ok = check_audio_files()
    
    # Check 2: Event priorities
    priorities_ok = check_event_priorities()
    
    # Check 3: Configuration
    config_ok = check_configuration()
    
    # Summary
    print("\n" + "="*50)
    print("📊 SETUP CHECK SUMMARY")
    print("="*50)
    
    print(f"Audio Files:      {'✅ PASS' if files_ok else '❌ FAIL'}")
    print(f"Event Priorities: {'✅ PASS' if priorities_ok else '❌ FAIL'}")
    print(f"Configuration:    {'✅ PASS' if config_ok else '❌ FAIL'}")
    
    all_passed = files_ok and priorities_ok and config_ok
    
    if all_passed:
        print("\n🎉 Setup check passed! Your 11 audio events are properly configured.")
        print("\n📋 Next steps:")
        print("   1. Install dependencies: pip install -r backend/requirements.txt")
        print("   2. Test the system: python start_services.py")
        print("   3. Upload a Mobile Legends video for processing")
    else:
        print("\n⚠️ Setup check failed. Please fix the issues above.")
    
    return all_passed


if __name__ == "__main__":
    main()
