#!/usr/bin/env python3
"""
Simple test to process video and generate clips with detailed logging.
"""

import sys
import os
import asyncio
import tempfile

# Add backend to path
sys.path.append('backend')

async def test_simple_processing():
    """Test simple video processing with detailed output."""
    print("🎬 Simple Video Processing Test")
    print("=" * 50)
    
    try:
        from worker.video_processor import VideoProcessor
        from shared.models import ProcessingJob, JobStatus
        import uuid
        from datetime import datetime
        
        # Test video path
        video_path = r"assets\samples\Hardest Gusion match in SOLO Q _ Mobile Legends.mp4"
        
        if not os.path.exists(video_path):
            print(f"❌ Test video not found: {video_path}")
            return False
        
        print(f"✅ Test video found: {video_path}")
        file_size = os.path.getsize(video_path) / (1024 * 1024)
        print(f"📊 File size: {file_size:.1f}MB")
        
        # Create a simple mock job
        job_id = str(uuid.uuid4())
        job = ProcessingJob(
            job_id=job_id,
            original_filename="test_video.mp4",
            file_size=int(file_size * 1024 * 1024),
            status=JobStatus.QUEUED,
            created_at=datetime.utcnow()
        )
        
        print(f"📋 Created job: {job_id}")
        
        # Create video processor
        processor = VideoProcessor(
            assets_path="assets",
            temp_path=None
        )
        
        print(f"✅ VideoProcessor created")
        
        # Test just the audio detection first
        print(f"\n🎵 Testing audio detection...")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            print(f"📁 Temp dir: {temp_dir}")
            
            # Copy video to temp dir
            import shutil
            temp_video = os.path.join(temp_dir, "test_video.mp4")
            shutil.copy2(video_path, temp_video)
            print(f"📹 Video copied to: {temp_video}")
            
            # Test audio extraction
            audio_path = os.path.join(temp_dir, "audio.wav")
            print(f"🎵 Extracting audio to: {audio_path}")
            
            audio_success = processor.audio_detector.extract_audio_from_video(temp_video, audio_path)
            if audio_success and os.path.exists(audio_path):
                audio_size = os.path.getsize(audio_path) / (1024 * 1024)
                print(f"✅ Audio extracted: {audio_size:.1f}MB")
                
                # Test audio event detection
                print(f"🔍 Detecting audio events...")
                audio_events = processor.audio_detector.detect_events(audio_path)
                print(f"🎯 Audio events found: {len(audio_events)}")
                
                for event in audio_events:
                    print(f"  🎵 {event.event_type} at {event.timestamp:.1f}s (confidence: {event.confidence:.2f})")
            else:
                print(f"❌ Audio extraction failed")
            
            # Test visual detection on a small sample
            print(f"\n👁️ Testing visual detection...")
            visual_events = processor.visual_detector.detect_events(temp_video)
            print(f"🎯 Visual events found: {len(visual_events)}")
            
            for event in visual_events:
                print(f"  👁️ {event.event_type} at {event.timestamp:.1f}s (confidence: {event.confidence:.2f})")
            
            # Test clip generation if we have events
            all_events = (audio_events if 'audio_events' in locals() else []) + visual_events
            
            if all_events:
                print(f"\n🎬 Testing clip generation for {len(all_events)} events...")
                
                clips_dir = os.path.join(temp_dir, "clips")
                os.makedirs(clips_dir, exist_ok=True)
                
                # Test generating one clip
                test_event = all_events[0]
                clip_path = os.path.join(clips_dir, f"test_clip_{test_event.event_type}.mp4")
                
                print(f"🎬 Generating test clip: {clip_path}")
                
                clip_success = await processor.clip_generator.generate_clip(
                    video_path=temp_video,
                    output_path=clip_path,
                    start_time=test_event.timestamp,
                    event_type=test_event.event_type,
                    duration=10.0
                )
                
                if clip_success and os.path.exists(clip_path):
                    clip_size = os.path.getsize(clip_path) / (1024 * 1024)
                    print(f"✅ Test clip generated: {clip_size:.1f}MB")
                    print(f"📁 Clip location: {clip_path}")
                    
                    # Copy clip to a permanent location for user to access
                    permanent_clip = os.path.join(os.getcwd(), f"test_clip_{test_event.event_type}_{job_id[:8]}.mp4")
                    shutil.copy2(clip_path, permanent_clip)
                    print(f"💾 Clip saved to: {permanent_clip}")
                    
                    return True
                else:
                    print(f"❌ Test clip generation failed")
            else:
                print(f"⚠️ No events detected - cannot test clip generation")
                print(f"💡 This might be normal if the video doesn't contain Mobile Legends events")
        
        return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    return asyncio.run(test_simple_processing())

if __name__ == "__main__":
    success = main()
    if success:
        print("\n🎉 Simple processing test completed successfully!")
        print("📁 Check the current directory for generated clip files!")
    else:
        print("\n❌ Simple processing test failed!")
    sys.exit(0 if success else 1)
