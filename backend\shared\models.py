"""Shared data models for the ML Highlights service."""

from enum import Enum
from typing import List, Optional, Dict, Any
from datetime import datetime
from pydantic import BaseModel, Field


class JobStatus(str, Enum):
    """Job processing status."""
    QUEUED = "queued"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    UNKNOWN = "unknown"


class EventType(str, Enum):
    """Types of game events that can be detected."""
    # Multi-kill events (audio) - multiple kills in quick succession
    FIRST_BLOOD = "first_blood"
    DOUBLE_KILL = "double_kill"
    TRIPLE_KILL = "triple_kill"
    MEGA_KILL = "mega_kill"
    MANIAC = "maniac"
    SAVAGE = "savage"

    # Kill spree events (audio) - kill streak over time
    KILLING_SPREE = "killing_spree"
    MONSTER_KILL = "monster_kill"
    GODLIKE = "godlike"
    LEGENDARY = "legendary"
    UNSTOPPABLE = "unstoppable"

    # General kill event (audio)
    HAS_SLAIN = "has_slain"

    # Objective events (visual)
    LORD_KILL = "lord_kill"
    TURTLE_KILL = "turtle_kill"
    CRYSTAL_DESTROY = "crystal_destroy"
    TOWER_DESTROY = "tower_destroy"


class DetectedEvent(BaseModel):
    """A detected game event with timestamp and confidence."""
    event_type: EventType
    timestamp: float = Field(..., description="Timestamp in seconds from video start")
    confidence: float = Field(..., ge=0.0, le=1.0, description="Detection confidence score")
    source: str = Field(..., description="Detection source: 'audio' or 'visual'")
    metadata: Dict[str, Any] = Field(default_factory=dict, description="Additional event metadata")


class VideoClip(BaseModel):
    """A generated video clip."""
    clip_id: str
    event_type: EventType
    start_time: float = Field(..., description="Start timestamp in seconds")
    duration: float = Field(..., description="Clip duration in seconds")
    file_path: str = Field(..., description="Path to the clip file")
    file_size: int = Field(..., description="File size in bytes")
    thumbnail_path: Optional[str] = None


class ProcessingJob(BaseModel):
    """A video processing job."""
    job_id: str
    status: JobStatus
    original_filename: str
    file_size: int
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    
    # Processing results
    detected_events: List[DetectedEvent] = Field(default_factory=list)
    generated_clips: List[VideoClip] = Field(default_factory=list)
    playlist_url: Optional[str] = None
    zip_url: Optional[str] = None
    
    # Processing metadata
    video_duration: Optional[float] = None
    processing_time: Optional[float] = None


class UploadResponse(BaseModel):
    """Response from file upload endpoint."""
    job_id: str
    upload_url: str
    status: JobStatus
    message: str


class JobStatusResponse(BaseModel):
    """Response from job status endpoint."""
    job_id: str
    status: JobStatus
    progress: Optional[float] = Field(None, ge=0.0, le=1.0, description="Processing progress (0-1)")
    message: Optional[str] = None
    error_message: Optional[str] = None
    
    # Results (only when completed)
    clips: List[VideoClip] = Field(default_factory=list)
    playlist_url: Optional[str] = None
    zip_url: Optional[str] = None
    
    # Metadata
    created_at: datetime
    processing_time: Optional[float] = None


class ClipGenerationConfig(BaseModel):
    """Configuration for clip generation."""
    clip_duration: float = Field(default=8.0, description="Duration of each clip in seconds")
    padding_before: float = Field(default=6.0, description="Seconds to include before event (where gameplay happens)")
    padding_after: float = Field(default=2.0, description="Seconds to include after event")
    target_aspect_ratio: str = Field(default="9:16", description="Target aspect ratio for clips")
    max_clips_per_event: int = Field(default=1, description="Maximum clips per event type")

    # Multi-kill chaining configuration
    chain_window: float = Field(default=10.0, description="Window to look for chained kills (seconds)")
    chain_padding_before: float = Field(default=8.0, description="Extra padding for chained multi-kills")
    
    # Quality settings
    video_codec: str = Field(default="libx264", description="Video codec for output")
    audio_codec: str = Field(default="aac", description="Audio codec for output")
    crf: int = Field(default=23, description="Constant Rate Factor for video quality")


class ProcessingConfig(BaseModel):
    """Configuration for the processing pipeline."""
    
    # Audio detection settings
    audio_similarity_threshold: float = Field(default=0.83, ge=0.0, le=1.0)
    audio_window_size: float = Field(default=2.0, description="Audio analysis window in seconds")
    audio_hop_size: float = Field(default=1.0, description="Audio analysis hop size in seconds")
    
    # Visual detection settings
    visual_similarity_threshold: float = Field(default=0.90, ge=0.0, le=1.0)
    visual_sample_rate: float = Field(default=3.0, description="Frames per second for visual analysis")
    
    # Event merging settings
    merge_window: float = Field(default=3.0, description="Merge events within this window (seconds)")
    
    # Clip generation
    clip_config: ClipGenerationConfig = Field(default_factory=ClipGenerationConfig)


# Event priority for ranking when multiple events are detected
EVENT_PRIORITY = {
    # Highest priority - rare multi-kills
    EventType.SAVAGE: 15,
    EventType.MANIAC: 14,
    EventType.MEGA_KILL: 13,
    EventType.TRIPLE_KILL: 12,
    EventType.DOUBLE_KILL: 11,

    # High priority - kill sprees (over time)
    EventType.UNSTOPPABLE: 10,
    EventType.LEGENDARY: 9,
    EventType.GODLIKE: 8,
    EventType.MONSTER_KILL: 7,
    EventType.KILLING_SPREE: 6,

    # Medium priority - special events
    EventType.FIRST_BLOOD: 5,

    # Lower priority - objectives
    EventType.LORD_KILL: 4,
    EventType.TURTLE_KILL: 3,
    EventType.CRYSTAL_DESTROY: 2,
    EventType.TOWER_DESTROY: 1,

    # Lowest priority - common events
    EventType.HAS_SLAIN: 0,
}


# Multi-kill chain definitions - events that should be chained together
MULTI_KILL_CHAINS = {
    EventType.DOUBLE_KILL: [EventType.HAS_SLAIN],
    EventType.TRIPLE_KILL: [EventType.HAS_SLAIN, EventType.DOUBLE_KILL],
    EventType.MEGA_KILL: [EventType.HAS_SLAIN, EventType.DOUBLE_KILL, EventType.TRIPLE_KILL],
    EventType.MANIAC: [EventType.HAS_SLAIN, EventType.DOUBLE_KILL, EventType.TRIPLE_KILL, EventType.MEGA_KILL],
    EventType.SAVAGE: [EventType.HAS_SLAIN, EventType.DOUBLE_KILL, EventType.TRIPLE_KILL, EventType.MEGA_KILL, EventType.MANIAC],
}


# Kill spree chains - events that build up over time (separate from multi-kills)
KILL_SPREE_CHAINS = {
    EventType.MONSTER_KILL: [EventType.KILLING_SPREE],
    EventType.GODLIKE: [EventType.KILLING_SPREE, EventType.MONSTER_KILL],
    EventType.LEGENDARY: [EventType.KILLING_SPREE, EventType.MONSTER_KILL, EventType.GODLIKE],
    EventType.UNSTOPPABLE: [EventType.KILLING_SPREE, EventType.MONSTER_KILL, EventType.GODLIKE, EventType.LEGENDARY],
}
