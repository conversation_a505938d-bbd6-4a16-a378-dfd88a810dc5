"""FastAPI Gateway Service for ML Highlights."""

import uuid
import json
from datetime import datetime
from typing import List, Optional
from fastapi import <PERSON>AP<PERSON>, HTTPException, UploadFile, File, Depends
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
import structlog

from shared.models import (
    JobStatus, ProcessingJob, UploadResponse, JobStatusResponse,
    VideoClip, ProcessingConfig
)
from shared.config import get_settings
from shared.storage import get_storage_client, StorageClient
from gateway.queue import get_queue_client, QueueClient

# Configure structured logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()

# Initialize FastAPI app
app = FastAPI(
    title="ML Highlights API",
    description="Extract TikTok-ready highlights from Mobile Legends match recordings",
    version="1.0.0"
)

# Configure CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],  # Configure appropriately for production
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

settings = get_settings()


@app.on_event("startup")
async def startup_event():
    """Initialize services on startup."""
    logger.info("Starting ML Highlights API Gateway", version="1.0.0")


@app.on_event("shutdown")
async def shutdown_event():
    """Cleanup on shutdown."""
    logger.info("Shutting down ML Highlights API Gateway")


@app.get("/")
async def root():
    """Health check endpoint."""
    return {
        "service": "ML Highlights API",
        "status": "healthy",
        "version": "1.0.0",
        "timestamp": datetime.utcnow().isoformat()
    }


@app.get("/health")
async def health_check():
    """Detailed health check."""
    try:
        # Test Redis connection (optional) with timeout
        redis_healthy = False
        try:
            import asyncio
            # Set a short timeout for Redis check
            queue_client = await asyncio.wait_for(get_queue_client(), timeout=2.0)
            # Try to perform a simple operation to test Redis with timeout
            await asyncio.wait_for(queue_client._get_redis(), timeout=2.0)
            redis_healthy = True
        except asyncio.TimeoutError:
            logger.warning("Redis health check timed out")
        except Exception as redis_error:
            logger.warning("Redis health check failed", error=str(redis_error))

        # For now, consider the service healthy even without Redis
        return {
            "status": "healthy",
            "services": {
                "redis": "healthy" if redis_healthy else "unavailable",
                "storage": "healthy",  # Assume storage is healthy if we got this far
                "api": "healthy"
            },
            "timestamp": datetime.utcnow().isoformat()
        }
    except Exception as e:
        logger.error("Health check failed", error=str(e))
        return JSONResponse(
            status_code=503,
            content={
                "status": "unhealthy",
                "error": str(e),
                "timestamp": datetime.utcnow().isoformat()
            }
        )


@app.post("/upload", response_model=UploadResponse)
async def upload_video(
    file: UploadFile = File(...),
    storage_client: StorageClient = Depends(get_storage_client)
):
    """
    Upload video file directly and initiate processing.

    Accepts the video file, uploads it to storage, and creates a processing job.
    """
    try:
        logger.info("Upload request received", filename=file.filename)

        # Get file info
        filename = file.filename or "unknown.mp4"
        file_size = 0

        # Read file content to get size
        logger.info("Reading file content", filename=filename)
        file_content = await file.read()
        file_size = len(file_content)
        logger.info("File read successfully", filename=filename, size=file_size)

        # Validate file size
        if file_size > settings.max_file_size:
            raise HTTPException(
                status_code=413,
                detail=f"File size {file_size} exceeds maximum allowed size {settings.max_file_size}"
            )

        # Validate file extension
        file_ext = filename.lower().split('.')[-1] if '.' in filename else ''
        if file_ext not in settings.allowed_extensions_list:
            raise HTTPException(
                status_code=400,
                detail=f"File extension '{file_ext}' not allowed. Allowed: {settings.allowed_extensions_list}"
            )
        
        # Generate job ID and S3 key
        job_id = str(uuid.uuid4())
        s3_key = storage_client.get_raw_video_key(job_id, filename)

        # Upload file directly to storage
        logger.info("Starting upload to storage", s3_key=s3_key, size=file_size)
        upload_success = await storage_client.upload_file_content(
            key=s3_key,
            file_content=file_content,
            content_type=f"video/{file_ext}"
        )

        if not upload_success:
            logger.error("Storage upload failed", s3_key=s3_key)
            raise HTTPException(
                status_code=500,
                detail="Failed to upload file to storage"
            )

        logger.info("Storage upload successful", s3_key=s3_key)
        
        # Create processing job
        job = ProcessingJob(
            job_id=job_id,
            status=JobStatus.QUEUED,
            original_filename=filename,
            file_size=file_size,
            created_at=datetime.utcnow()
        )
        
        # Store job in Redis (optional - gracefully handle if Redis is not available)
        try:
            queue_client = await get_queue_client()
            await queue_client.store_job(job)
            # Enqueue processing task (will be picked up after upload completes)
            await queue_client.enqueue_job(job_id, s3_key)
            queue_status = "queued for processing"
        except Exception as e:
            logger.warning("Failed to queue job (Redis not available)", error=str(e))
            queue_status = "uploaded (processing queue unavailable)"
        
        logger.info(
            "Created upload job",
            job_id=job_id,
            filename=filename,
            file_size=file_size,
            s3_key=s3_key
        )
        
        return UploadResponse(
            job_id=job_id,
            upload_url=f"s3://{storage_client.bucket}/{s3_key}",  # For reference
            status=JobStatus.QUEUED,
            message=f"File uploaded successfully. Status: {queue_status}"
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to create upload job", error=str(e), filename=filename)
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/status/{job_id}", response_model=JobStatusResponse)
async def get_job_status(
    job_id: str,
    storage_client: StorageClient = Depends(get_storage_client)
):
    """Get the status of a processing job."""
    try:
        # Retrieve job from Redis (if available)
        try:
            queue_client = await get_queue_client()
            job = await queue_client.get_job(job_id)
        except Exception as e:
            logger.warning("Failed to connect to Redis for job status", error=str(e))
            # Return a basic response when Redis is not available
            return JobStatusResponse(
                job_id=job_id,
                status=JobStatus.UNKNOWN,
                message="Job status unavailable (Redis not connected)",
                progress=0,
                generated_clips=[]
            )

        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Generate download URLs for completed clips
        clips_with_urls = []
        if job.status == JobStatus.COMPLETED and job.generated_clips:
            for clip in job.generated_clips:
                # Generate presigned download URL
                download_url = await storage_client.generate_presigned_download_url(
                    clip.file_path,
                    expires_in=3600  # 1 hour
                )
                clip_with_url = clip.model_copy()
                clip_with_url.file_path = download_url
                clips_with_urls.append(clip_with_url)
        
        # Calculate progress
        progress = None
        if job.status == JobStatus.PROCESSING:
            # Simple progress estimation based on detected events
            if job.detected_events:
                progress = min(0.8, len(job.detected_events) * 0.1)
            else:
                progress = 0.1
        elif job.status == JobStatus.COMPLETED:
            progress = 1.0
        
        return JobStatusResponse(
            job_id=job_id,
            status=job.status,
            progress=progress,
            message=_get_status_message(job.status),
            error_message=job.error_message,
            clips=clips_with_urls,
            playlist_url=job.playlist_url,
            zip_url=job.zip_url,
            created_at=job.created_at,
            processing_time=job.processing_time
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Failed to get job status", job_id=job_id, error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


@app.get("/jobs", response_model=List[JobStatusResponse])
async def list_jobs(
    limit: int = 50,
    offset: int = 0,
    status: Optional[JobStatus] = None,
    queue_client: QueueClient = Depends(get_queue_client)
):
    """List processing jobs with optional filtering."""
    try:
        jobs = await queue_client.list_jobs(limit=limit, offset=offset, status=status)
        
        # Convert to response format
        job_responses = []
        for job in jobs:
            progress = None
            if job.status == JobStatus.PROCESSING:
                progress = 0.5  # Simplified progress
            elif job.status == JobStatus.COMPLETED:
                progress = 1.0
            
            job_responses.append(JobStatusResponse(
                job_id=job.job_id,
                status=job.status,
                progress=progress,
                message=_get_status_message(job.status),
                error_message=job.error_message,
                clips=job.generated_clips,
                playlist_url=job.playlist_url,
                zip_url=job.zip_url,
                created_at=job.created_at,
                processing_time=job.processing_time
            ))
        
        return job_responses
        
    except Exception as e:
        logger.error("Failed to list jobs", error=str(e))
        raise HTTPException(status_code=500, detail="Internal server error")


def _get_status_message(status: JobStatus) -> str:
    """Get a human-readable status message."""
    messages = {
        JobStatus.QUEUED: "Job is queued for processing",
        JobStatus.PROCESSING: "Video is being analyzed for highlights",
        JobStatus.COMPLETED: "Highlights have been generated and are ready for download",
        JobStatus.FAILED: "Processing failed"
    }
    return messages.get(status, "Unknown status")


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host=settings.api_host,
        port=settings.api_port,
        reload=settings.debug,
        log_level=settings.log_level.lower()
    )
