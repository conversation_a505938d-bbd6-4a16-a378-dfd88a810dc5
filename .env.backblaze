# Backblaze B2 Configuration for ML Highlights
# Copy this to .env and update with your actual values

# Storage Provider
STORAGE_PROVIDER=backblaze

# Backblaze B2 Credentials
S3_BUCKET=MLhighlights
S3_ACCESS_KEY_ID=005a665ff0e7c360000000003
S3_SECRET_ACCESS_KEY=K005w4ikLOftDCqa3/SuRqQcTdzV6Uk
S3_REGION=us-west-004
S3_ENDPOINT_URL=https://s3.us-west-004.backblazeb2.com

# Redis Configuration
REDIS_URL=redis://localhost:6379

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000

# Frontend Configuration
VITE_API_URL=http://localhost:8000

# Worker Configuration
MAX_WORKERS=2
WORKER_TIMEOUT=300

# File Upload Limits
MAX_FILE_SIZE=**********  # 2GB in bytes
ALLOWED_EXTENSIONS=mp4,avi,mov,mkv

# Processing Configuration
AUDIO_SIMILARITY_THRESHOLD=0.83
VISUAL_SIMILARITY_THRESHOLD=0.90
CLIP_DURATION=8.0
CLIP_PADDING=6.0

# Development
DEBUG=true
LOG_LEVEL=INFO

# Paths
ASSETS_PATH=assets
TEMP_PATH=temp

# IMPORTANT NOTES:
# 1. Replace S3_SECRET_ACCESS_KEY with your actual Backblaze application key
# 2. Replace S3_BUCKET with your actual bucket name
# 3. Update S3_REGION and S3_ENDPOINT_URL to match your bucket's region
# 4. Never commit this file to version control with real credentials
