#!/usr/bin/env python3
"""
Whisper-based audio detection for Mobile Legends announcer events.
Best-in-class approach using speech recognition instead of audio similarity.
"""

import os
import tempfile
from typing import List, Dict, Set
import structlog
import numpy as np

from shared.models import DetectedEvent, EventType, ProcessingConfig

logger = structlog.get_logger()


class WhisperAudioDetector:
    """Whisper-based audio event detector for Mobile Legends."""
    
    def __init__(self, config: ProcessingConfig):
        self.config = config
        self.model = None
        
        # Mobile Legends hotwords/keywords for each event type
        # Include variations that <PERSON>hisper might transcribe
        self.hotwords = {
            EventType.FIRST_BLOOD: {"first", "blood", "firstblood"},
            EventType.DOUBLE_KILL: {"double", "kill", "doublekill", "co-kill", "co", "kill"},  # Whisper transcribes as "Co-kill"
            EventType.TRIPLE_KILL: {"triple", "kill", "triplekill"},
            EventType.MEGA_KILL: {"mega", "kill", "megakill"},
            EventType.MANIAC: {"maniac"},
            EventType.SAVAGE: {"savage"},
            EventType.KILLING_SPREE: {"killing", "spree", "killingspree"},
            EventType.MONSTER_KILL: {"monster", "kill", "monsterkill"},
            EventType.GODLIKE: {"godlike", "god", "like"},  # Whisper transcribes as "God like"
            EventType.LEGENDARY: {"legendary"},
            EventType.UNSTOPPABLE: {"unstoppable"},
            EventType.HAS_SLAIN: {"slain", "has"}
        }
        
        # Flatten all hotwords for faster lookup
        self.all_hotwords = set()
        for words in self.hotwords.values():
            self.all_hotwords.update(words)
        
        logger.info("Whisper audio detector initialized", 
                   hotwords_count=len(self.all_hotwords),
                   event_types=len(self.hotwords))
    
    def _load_model(self):
        """Load Whisper model on first use."""
        if self.model is None:
            try:
                from faster_whisper import WhisperModel
                
                # Use tiny.en model for speed (quantized for even better performance)
                self.model = WhisperModel(
                    "tiny.en", 
                    compute_type="int8",  # Quantized for speed
                    device="cpu"  # CPU is fine for tiny model
                )
                
                logger.info("Whisper model loaded", model="tiny.en", compute_type="int8")
                
            except ImportError:
                logger.error("faster-whisper not installed. Install with: pip install faster-whisper")
                raise
            except Exception as e:
                logger.error("Failed to load Whisper model", error=str(e))
                raise
    
    def extract_audio_from_video(self, video_path: str, output_path: str) -> bool:
        """Extract audio from video using ffmpeg (optimized for speech recognition)."""
        try:
            import subprocess
            
            # Find ffmpeg executable
            ffmpeg_cmd = self._find_ffmpeg()
            if not ffmpeg_cmd:
                logger.error("FFmpeg not found in PATH")
                return False
            
            # Extract audio optimized for speech recognition
            cmd = [
                ffmpeg_cmd, "-i", video_path,
                "-vn",  # No video
                "-acodec", "pcm_s16le",  # PCM 16-bit for Whisper
                "-ar", "16000",  # 16kHz sample rate (Whisper standard)
                "-ac", "1",  # Mono
                "-y",  # Overwrite output
                output_path
            ]
            
            result = subprocess.run(
                cmd,
                capture_output=True,
                text=True,
                timeout=300  # 5 minute timeout
            )
            
            if result.returncode == 0:
                logger.info("Extracted audio from video", 
                           output_path=output_path, 
                           video_path=video_path)
                return True
            else:
                logger.error("Audio extraction failed", 
                           error=result.stderr,
                           returncode=result.returncode)
                return False
                
        except subprocess.TimeoutExpired:
            logger.error("Audio extraction timed out")
            return False
        except Exception as e:
            logger.error("Audio extraction failed", error=str(e))
            return False
    
    def detect_events(self, audio_path: str) -> List[DetectedEvent]:
        """Detect Mobile Legends events using Whisper speech recognition."""
        try:
            self._load_model()
            
            logger.info("Starting Whisper transcription", audio_path=audio_path)
            
            # Transcribe with word timestamps (VAD disabled for game audio)
            segments, info = self.model.transcribe(
                audio_path,
                vad_filter=False,  # Disable VAD for game announcer audio
                word_timestamps=True,
                language="en",
                condition_on_previous_text=False,  # Better for short phrases
                temperature=0.0,  # Deterministic output
                compression_ratio_threshold=2.4,
                log_prob_threshold=-1.0,
                no_speech_threshold=0.3  # Lower threshold for game audio
            )
            
            detected_events = []
            
            # Process each segment
            for segment in segments:
                if hasattr(segment, 'words') and segment.words:
                    # Also check the full segment text for phrase matching
                    segment_text = segment.text.lower().strip()
                    segment_start = segment.start

                    # Check for phrase matches in the full segment
                    for event_type, keywords in self.hotwords.items():
                        for keyword in keywords:
                            if keyword in segment_text:
                                detected_events.append(DetectedEvent(
                                    event_type=event_type,
                                    timestamp=float(segment_start),
                                    confidence=0.8,  # High confidence for phrase matches
                                    source="whisper",
                                    metadata={
                                        "matched_keyword": keyword,
                                        "segment_text": segment.text.strip(),
                                        "match_type": "phrase"
                                    }
                                ))

                                logger.info("Detected Whisper event (phrase)",
                                           event_type=event_type.value,
                                           keyword=keyword,
                                           segment_text=segment_text,
                                           timestamp=segment_start)
                                break  # Only match once per event type per segment

                    # Also check individual words
                    if hasattr(segment, 'words') and segment.words:
                        for word in segment.words:
                            word_text = word.word.lower().strip()
                            word_start = word.start
                            word_probability = getattr(word, 'probability', 0.0)

                            # Check if this word matches any hotwords
                            if word_text in self.all_hotwords and word_probability > 0.3:  # Lower threshold
                                # Find which event type this word belongs to
                                for event_type, keywords in self.hotwords.items():
                                    if word_text in keywords:
                                        detected_events.append(DetectedEvent(
                                            event_type=event_type,
                                            timestamp=float(word_start),
                                            confidence=float(word_probability),
                                            source="whisper",
                                            metadata={
                                                "word": word_text,
                                                "segment_text": segment.text.strip(),
                                                "word_probability": word_probability,
                                                "match_type": "word"
                                            }
                                        ))

                                        logger.info("Detected Whisper event (word)",
                                                   event_type=event_type.value,
                                                   word=word_text,
                                                   timestamp=word_start,
                                                   probability=word_probability)
                                        break  # Only match once per event type per word
            
            # Merge nearby events of the same type
            merged_events = self._merge_nearby_events(detected_events)
            
            logger.info("Whisper detection complete",
                       audio_path=audio_path,
                       total_events=len(merged_events),
                       event_types=[e.event_type.value for e in merged_events])
            
            return merged_events
            
        except Exception as e:
            logger.error("Whisper detection failed", error=str(e))
            import traceback
            traceback.print_exc()
            return []
    
    def _merge_nearby_events(self, events: List[DetectedEvent], threshold: float = 2.0) -> List[DetectedEvent]:
        """Merge events of the same type that are within threshold seconds."""
        if not events:
            return []
        
        # Sort by timestamp
        events.sort(key=lambda e: e.timestamp)
        
        merged = []
        current_event = events[0]
        
        for next_event in events[1:]:
            # If same event type and within threshold, merge (keep higher confidence)
            if (current_event.event_type == next_event.event_type and 
                abs(next_event.timestamp - current_event.timestamp) <= threshold):
                
                if next_event.confidence > current_event.confidence:
                    current_event = next_event
            else:
                merged.append(current_event)
                current_event = next_event
        
        merged.append(current_event)
        
        logger.info("Merged nearby events", 
                   original_count=len(events), 
                   merged_count=len(merged))
        
        return merged
    
    def _find_ffmpeg(self) -> str:
        """Find FFmpeg executable in common locations."""
        import shutil
        import os
        
        # Try common names
        for name in ["ffmpeg", "ffmpeg.exe"]:
            # Check if in PATH
            if shutil.which(name):
                return name
        
        # Check common Windows installation paths
        common_paths = [
            r"C:\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files\ffmpeg\bin\ffmpeg.exe",
            r"C:\Program Files (x86)\ffmpeg\bin\ffmpeg.exe",
            # Winget installation path
            os.path.expanduser(r"~\AppData\Local\Microsoft\WinGet\Packages\Gyan.FFmpeg_Microsoft.Winget.Source_8wekyb3d8bbwe\ffmpeg-7.1.1-full_build\bin\ffmpeg.exe")
        ]
        
        for path in common_paths:
            if os.path.exists(path):
                return path
        
        return None
