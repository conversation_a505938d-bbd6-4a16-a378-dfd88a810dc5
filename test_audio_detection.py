#!/usr/bin/env python3
"""
Test audio detection with improved thresholds.
"""

import sys
import os
import tempfile

# Add backend to path
sys.path.append('backend')

def test_audio_detection():
    """Test audio detection with new settings."""
    print("🎵 Testing Improved Audio Detection")
    print("=" * 50)
    
    try:
        from worker.audio_detector import AudioEventDetector
        from shared.models import ProcessingConfig
        
        # Test video path
        video_path = r"assets\samples\Hardest Gusion match in SOLO Q _ Mobile Legends.mp4"
        
        if not os.path.exists(video_path):
            print(f"❌ Test video not found: {video_path}")
            return False
        
        print(f"✅ Test video found: {video_path}")
        
        # Create audio detector with improved settings
        config = ProcessingConfig()
        print(f"🔧 Audio settings:")
        print(f"   📊 Similarity threshold: {config.audio_similarity_threshold}")
        print(f"   ⏱️ Window size: {config.audio_window_size}s")
        print(f"   🔄 Hop size: {config.audio_hop_size}s")
        
        detector = AudioEventDetector(assets_path="assets", config=config)
        print(f"✅ Audio detector created")
        print(f"📚 Reference samples loaded: {len(detector.reference_specs)}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Extract audio
            audio_path = os.path.join(temp_dir, "test_audio.wav")
            print(f"\n🎵 Extracting audio...")
            
            success = detector.extract_audio_from_video(video_path, audio_path)
            if not success or not os.path.exists(audio_path):
                print(f"❌ Audio extraction failed")
                return False
            
            audio_size = os.path.getsize(audio_path) / (1024 * 1024)
            print(f"✅ Audio extracted: {audio_size:.1f}MB")
            
            # Test detection on first 60 seconds only
            print(f"\n🔍 Running audio detection (first 60 seconds)...")
            
            # Temporarily modify the audio file to only process first 60 seconds
            import librosa
            import soundfile as sf
            
            audio_data, sr = librosa.load(audio_path, sr=None)
            duration = len(audio_data) / sr
            print(f"📊 Full audio duration: {duration:.1f}s")
            
            # Take only first 60 seconds
            test_duration = min(60, duration)
            test_samples = int(test_duration * sr)
            test_audio = audio_data[:test_samples]
            
            test_audio_path = os.path.join(temp_dir, "test_60s.wav")
            sf.write(test_audio_path, test_audio, sr)
            print(f"🎵 Testing on {test_duration:.1f}s sample")
            
            # Detect events
            events = detector.detect_events(test_audio_path)
            
            print(f"\n🎯 Detection Results:")
            print(f"📊 Total events detected: {len(events)}")
            
            if len(events) == 0:
                print(f"✅ Good! No false positives detected")
                return True
            elif len(events) <= 5:
                print(f"⚠️ Moderate: {len(events)} events detected")
                print(f"💡 This might be reasonable for a 60-second Mobile Legends clip")
                
                for i, event in enumerate(events, 1):
                    print(f"  {i}. {event.event_type} at {event.timestamp:.1f}s (confidence: {event.confidence:.3f})")
                
                return True
            else:
                print(f"❌ Too many events: {len(events)} in {test_duration}s")
                print(f"🔧 Audio detection still needs tuning")
                
                # Show first 10 events
                for i, event in enumerate(events[:10], 1):
                    print(f"  {i}. {event.event_type} at {event.timestamp:.1f}s (confidence: {event.confidence:.3f})")
                
                if len(events) > 10:
                    print(f"  ... and {len(events) - 10} more events")
                
                return False
        
    except Exception as e:
        print(f"❌ Audio detection test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    success = test_audio_detection()
    if success:
        print("\n🎉 Audio detection test passed!")
        print("✅ Audio detection is working properly")
    else:
        print("\n❌ Audio detection test failed!")
        print("🔧 Audio detection needs further tuning")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
