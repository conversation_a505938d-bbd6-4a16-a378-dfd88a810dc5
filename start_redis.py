#!/usr/bin/env python3
"""
Redis startup script for ML Highlights.
Starts the Redis server for job queue management.
"""

import os
import sys
import subprocess
from pathlib import Path

def start_redis():
    """Start Redis server."""
    print("🔴 Starting Redis Server")
    print("=" * 50)
    
    # Check if Redis directory exists
    redis_dir = Path("redis")
    if not redis_dir.exists():
        print("❌ Redis not found! Please run the installation first.")
        print("   Download from: https://github.com/tporadowski/redis/releases")
        return False
    
    redis_server = redis_dir / "redis-server.exe"
    redis_config = redis_dir / "redis.windows.conf"
    
    if not redis_server.exists():
        print(f"❌ Redis server not found at: {redis_server}")
        return False
    
    if not redis_config.exists():
        print(f"❌ Redis config not found at: {redis_config}")
        return False
    
    print(f"📂 Redis directory: {redis_dir.absolute()}")
    print(f"⚙️ Config file: {redis_config}")
    print(f"🌐 Redis will run on: localhost:6379")
    print("\n" + "="*50)
    print("🔄 Starting Redis server... (Press Ctrl+C to stop)")
    print("="*50)
    
    try:
        # Start Redis server
        cmd = [str(redis_server), str(redis_config)]
        result = subprocess.run(cmd)
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Redis stopped by user")
        return True
    except Exception as e:
        print(f"❌ Failed to start Redis: {e}")
        return False

def main():
    """Main function."""
    if start_redis():
        print("✅ Redis stopped successfully")
        return 0
    else:
        print("❌ Redis failed to start")
        return 1

if __name__ == "__main__":
    sys.exit(main())
