"""Cloud storage utilities for S3/GCS integration."""

import os
import asyncio
from typing import Optional, BinaryIO
from datetime import datetime, timedelta
import boto3
from botocore.exceptions import ClientError
import structlog

from .config import get_settings

logger = structlog.get_logger()


class StorageClient:
    """Unified storage client for S3 operations."""
    
    def __init__(self):
        self.settings = get_settings()

        # Configure S3 client for AWS or Backblaze B2
        client_config = {
            'aws_access_key_id': self.settings.s3_access_key_id,
            'aws_secret_access_key': self.settings.s3_secret_access_key,
            'region_name': self.settings.s3_region
        }

        # Add endpoint URL for Backblaze B2 or other S3-compatible services
        if self.settings.s3_endpoint_url:
            client_config['endpoint_url'] = self.settings.s3_endpoint_url

        self.s3_client = boto3.client('s3', **client_config)
        self.bucket = self.settings.s3_bucket
        self.storage_provider = self.settings.storage_provider

        logger.info(
            "Initialized storage client",
            provider=self.storage_provider,
            bucket=self.bucket,
            endpoint=self.settings.s3_endpoint_url or "default",
            region=self.settings.s3_region
        )
    
    async def generate_presigned_upload_url(
        self, 
        key: str, 
        content_type: str = "video/mp4",
        expires_in: int = 3600
    ) -> str:
        """Generate a presigned URL for uploading files to S3."""
        try:
            # Run the synchronous boto3 call in a thread pool
            loop = asyncio.get_event_loop()
            url = await loop.run_in_executor(
                None,
                lambda: self.s3_client.generate_presigned_url(
                    'put_object',
                    Params={
                        'Bucket': self.bucket,
                        'Key': key,
                        'ContentType': content_type
                    },
                    ExpiresIn=expires_in
                )
            )
            logger.info("Generated presigned upload URL", key=key, expires_in=expires_in)
            return url
        except ClientError as e:
            logger.error("Failed to generate presigned URL", key=key, error=str(e))
            raise
    
    async def generate_presigned_download_url(
        self, 
        key: str, 
        expires_in: int = 3600
    ) -> str:
        """Generate a presigned URL for downloading files from S3."""
        try:
            loop = asyncio.get_event_loop()
            url = await loop.run_in_executor(
                None,
                lambda: self.s3_client.generate_presigned_url(
                    'get_object',
                    Params={
                        'Bucket': self.bucket,
                        'Key': key
                    },
                    ExpiresIn=expires_in
                )
            )
            logger.info("Generated presigned download URL", key=key, expires_in=expires_in)
            return url
        except ClientError as e:
            logger.error("Failed to generate presigned download URL", key=key, error=str(e))
            raise
    
    async def upload_file(self, local_path: str, key: str, content_type: Optional[str] = None) -> bool:
        """Upload a file to S3."""
        try:
            extra_args = {}
            if content_type:
                extra_args['ContentType'] = content_type
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.s3_client.upload_file(local_path, self.bucket, key, ExtraArgs=extra_args)
            )
            logger.info("Uploaded file to S3", local_path=local_path, key=key)
            return True
        except ClientError as e:
            logger.error("Failed to upload file", local_path=local_path, key=key, error=str(e))
            return False

    async def upload_file_content(self, key: str, file_content: bytes, content_type: Optional[str] = None) -> bool:
        """Upload file content directly to S3."""
        try:
            extra_args = {}
            if content_type:
                extra_args['ContentType'] = content_type

            # Use BytesIO to create a file-like object from bytes
            from io import BytesIO
            file_obj = BytesIO(file_content)

            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.s3_client.upload_fileobj(file_obj, self.bucket, key, ExtraArgs=extra_args)
            )
            logger.info("Uploaded file content to S3", key=key, size=len(file_content))
            return True
        except ClientError as e:
            logger.error("Failed to upload file content", key=key, error=str(e))
            return False

    async def download_file(self, key: str, local_path: str) -> bool:
        """Download a file from S3."""
        try:
            # Ensure the directory exists
            os.makedirs(os.path.dirname(local_path), exist_ok=True)
            
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.s3_client.download_file(self.bucket, key, local_path)
            )
            logger.info("Downloaded file from S3", key=key, local_path=local_path)
            return True
        except ClientError as e:
            logger.error("Failed to download file", key=key, local_path=local_path, error=str(e))
            return False
    
    async def file_exists(self, key: str) -> bool:
        """Check if a file exists in S3."""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.s3_client.head_object(Bucket=self.bucket, Key=key)
            )
            return True
        except ClientError as e:
            if e.response['Error']['Code'] == '404':
                return False
            logger.error("Error checking file existence", key=key, error=str(e))
            raise
    
    async def delete_file(self, key: str) -> bool:
        """Delete a file from S3."""
        try:
            loop = asyncio.get_event_loop()
            await loop.run_in_executor(
                None,
                lambda: self.s3_client.delete_object(Bucket=self.bucket, Key=key)
            )
            logger.info("Deleted file from S3", key=key)
            return True
        except ClientError as e:
            logger.error("Failed to delete file", key=key, error=str(e))
            return False
    
    def get_raw_video_key(self, job_id: str, filename: str) -> str:
        """Generate S3 key for raw video upload."""
        return f"{self.settings.s3_raw_prefix}{job_id}/{filename}"
    
    def get_clip_key(self, job_id: str, clip_id: str, extension: str = "mp4") -> str:
        """Generate S3 key for a video clip."""
        return f"{self.settings.s3_clips_prefix}{job_id}/{clip_id}.{extension}"
    
    def get_zip_key(self, job_id: str) -> str:
        """Generate S3 key for the clips zip file."""
        return f"{self.settings.s3_zips_prefix}{job_id}/highlights.zip"
    
    def get_playlist_key(self, job_id: str) -> str:
        """Generate S3 key for the playlist JSON file."""
        return f"{self.settings.s3_clips_prefix}{job_id}/playlist.json"


# Global storage client instance
storage_client = StorageClient()


def get_storage_client() -> StorageClient:
    """Get the global storage client instance."""
    return storage_client
