#!/usr/bin/env python3
"""
Development backend starter for ML Highlights.
Runs the FastAPI backend without <PERSON>er/<PERSON>is for testing.
"""

import os
import sys
import subprocess
from pathlib import Path

def start_backend():
    """Start the FastAPI backend in development mode."""
    print("🚀 Starting ML Highlights Backend (Development Mode)")
    print("=" * 60)
    
    # Change to backend directory
    backend_dir = Path("backend")
    if not backend_dir.exists():
        print("❌ Backend directory not found!")
        return False
    
    # Set environment variables for development
    os.environ["PYTHONPATH"] = str(Path.cwd() / "backend")
    os.environ["REDIS_URL"] = "redis://localhost:6379"  # Will gracefully fail if Redis not available
    
    print("📂 Backend directory:", backend_dir.absolute())
    print("🌐 Starting API server at: http://localhost:8000")
    print("📚 API docs will be at: http://localhost:8000/docs")
    print("\n" + "="*60)
    print("🔄 Starting server... (Press Ctrl+C to stop)")
    print("="*60)
    
    try:
        # Start uvicorn from the backend directory
        cmd = [
            sys.executable, "-m", "uvicorn", 
            "gateway.main:app",
            "--host", "0.0.0.0",
            "--port", "8000",
            "--reload",
            "--reload-dir", ".",
        ]
        
        # Run from backend directory
        result = subprocess.run(cmd, cwd=backend_dir)
        return result.returncode == 0
        
    except KeyboardInterrupt:
        print("\n👋 Backend stopped by user")
        return True
    except Exception as e:
        print(f"❌ Failed to start backend: {e}")
        return False

def main():
    """Main function."""
    if start_backend():
        print("✅ Backend stopped successfully")
        return 0
    else:
        print("❌ Backend failed to start")
        return 1

if __name__ == "__main__":
    sys.exit(main())
