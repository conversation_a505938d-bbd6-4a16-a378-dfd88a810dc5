#!/usr/bin/env python3
"""Smart dependency installer that handles Python version compatibility."""

import sys
import subprocess
import os
from pathlib import Path


def get_python_version():
    """Get the current Python version."""
    return sys.version_info


def install_dependencies():
    """Install dependencies with Python version compatibility handling."""
    python_version = get_python_version()
    print(f"🐍 Detected Python {python_version.major}.{python_version.minor}.{python_version.micro}")
    
    # Check if we're using Python 3.13+
    if python_version >= (3, 13):
        print("⚠️ Python 3.13+ detected - using compatibility mode")
        return install_python313_compatible()
    else:
        print("✅ Using standard requirements.txt")
        return install_standard_requirements()


def install_python313_compatible():
    """Install dependencies compatible with Python 3.13+."""
    print("📦 Installing Python 3.13+ compatible packages...")
    
    # Core packages that work well with Python 3.13
    packages = [
        # Web framework
        "fastapi>=0.115.0",
        "uvicorn[standard]>=0.32.0",
        "python-multipart>=0.0.17",
        
        # Queue and caching
        "redis>=5.2.0",
        "rq>=1.17.0",
        
        # Cloud storage
        "boto3>=1.35.0",
        
        # Video/Audio processing (install these separately to handle build issues)
        "numpy>=1.26.0",
        "opencv-python>=4.10.0",
        "soundfile>=0.12.1",
        
        # Image processing
        "Pillow>=11.0.0",
        
        # Utilities
        "python-dotenv>=1.0.0",
        "pydantic>=2.10.0",
        "pydantic-settings>=2.7.0",
        "httpx>=0.28.0",
        "aiofiles>=24.0.0",
        
        # Logging
        "structlog>=24.0.0",
        
        # Development
        "pytest>=8.3.0",
        "pytest-asyncio>=0.24.0",
    ]
    
    # Install packages one by one to handle any build issues
    failed_packages = []
    
    for package in packages:
        try:
            print(f"  Installing {package}...")
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", package
            ], capture_output=True, text=True, timeout=300)
            
            if result.returncode == 0:
                print(f"  ✅ {package}")
            else:
                print(f"  ❌ {package} - {result.stderr.strip()}")
                failed_packages.append(package)
                
        except subprocess.TimeoutExpired:
            print(f"  ⏰ {package} - Installation timeout")
            failed_packages.append(package)
        except Exception as e:
            print(f"  ❌ {package} - {str(e)}")
            failed_packages.append(package)
    
    # Try to install problematic packages with special handling
    problematic_packages = {
        "librosa": "librosa>=0.10.0",
        "scipy": "scipy>=1.13.0", 
        "scikit-learn": "scikit-learn>=1.6.0",
        "scikit-image": "scikit-image>=0.24.0"
    }
    
    print("\n🔧 Installing packages that may need special handling...")
    for name, package in problematic_packages.items():
        try:
            print(f"  Installing {package}...")
            # Try with no build isolation first
            result = subprocess.run([
                sys.executable, "-m", "pip", "install", 
                "--no-build-isolation", package
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print(f"  ✅ {package}")
            else:
                # Try with pre-compiled wheel
                print(f"  Trying pre-compiled wheel for {name}...")
                result2 = subprocess.run([
                    sys.executable, "-m", "pip", "install", 
                    "--only-binary=all", package
                ], capture_output=True, text=True, timeout=300)
                
                if result2.returncode == 0:
                    print(f"  ✅ {package} (pre-compiled)")
                else:
                    print(f"  ⚠️ {package} - May need manual installation")
                    failed_packages.append(package)
                    
        except Exception as e:
            print(f"  ❌ {package} - {str(e)}")
            failed_packages.append(package)
    
    if failed_packages:
        print(f"\n⚠️ Some packages failed to install: {failed_packages}")
        print("You may need to install these manually or use conda.")
        return False
    else:
        print("\n✅ All packages installed successfully!")
        return True


def install_standard_requirements():
    """Install from requirements.txt for older Python versions."""
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "backend/requirements.txt"
        ], check=True, timeout=600)
        print("✅ Dependencies installed successfully!")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Installation failed: {e}")
        return False
    except subprocess.TimeoutExpired:
        print("❌ Installation timeout")
        return False


def check_critical_packages():
    """Check if critical packages are installed and working."""
    critical_packages = [
        "fastapi", "uvicorn", "redis", "boto3", "numpy", "opencv-python"
    ]
    
    print("\n🔍 Checking critical packages...")
    missing = []
    
    for package in critical_packages:
        try:
            __import__(package.replace("-", "_"))
            print(f"  ✅ {package}")
        except ImportError:
            print(f"  ❌ {package}")
            missing.append(package)
    
    if missing:
        print(f"\n⚠️ Missing packages: {missing}")
        print("Try running this script again or install manually.")
        return False
    else:
        print("\n✅ All critical packages are available!")
        return True


def main():
    """Main installation function."""
    print("🚀 ML Highlights Dependency Installer\n")
    
    # Check if we're in the right directory
    if not os.path.exists("backend/requirements.txt"):
        print("❌ Please run this script from the ML Highlights root directory")
        return False
    
    # Upgrade pip first
    print("📦 Upgrading pip...")
    try:
        subprocess.run([
            sys.executable, "-m", "pip", "install", "--upgrade", "pip"
        ], check=True, timeout=120)
        print("✅ Pip upgraded")
    except Exception as e:
        print(f"⚠️ Pip upgrade failed: {e}")
    
    print()
    
    # Install dependencies
    success = install_dependencies()
    
    if success:
        # Check critical packages
        success = check_critical_packages()
    
    if success:
        print("\n🎉 Installation completed successfully!")
        print("\n📋 Next steps:")
        print("1. Configure your .env file with Backblaze B2 credentials")
        print("2. Add reference assets to assets/ directory")
        print("3. Run: python start_services.py")
    else:
        print("\n❌ Installation had issues. Please check the errors above.")
        print("\nAlternative solutions:")
        print("1. Use Python 3.11 or 3.12 instead of 3.13")
        print("2. Use conda: conda install -c conda-forge <package-name>")
        print("3. Install packages individually with pip")
    
    return success


if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
