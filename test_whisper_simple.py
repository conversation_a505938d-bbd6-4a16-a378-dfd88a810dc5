#!/usr/bin/env python3
"""
Simple test of improved Whisper detection.
"""

import sys
import os
import tempfile

# Add backend to path
sys.path.append('backend')

def test_whisper_simple():
    """Simple test of Whisper detection."""
    print("🎤 Simple Whisper Test")
    print("=" * 30)
    
    try:
        from worker.whisper_audio_detector import WhisperAudioDetector
        from shared.models import ProcessingConfig
        import librosa
        import soundfile as sf
        import numpy as np
        
        # Create detector
        config = ProcessingConfig()
        detector = WhisperAudioDetector(config)
        print("✅ Whisper detector created")
        
        # Test one file
        audio_path = "assets/audio/first_blood.wav"
        
        if not os.path.exists(audio_path):
            print(f"❌ File not found: {audio_path}")
            return False
        
        print(f"🎵 Testing: {audio_path}")
        
        with tempfile.TemporaryDirectory() as temp_dir:
            # Prepare audio
            audio_data, sr = librosa.load(audio_path, sr=None)
            silence = np.zeros(int(1 * sr))
            test_audio = np.concatenate([silence, audio_data, silence])
            test_audio_16k = librosa.resample(test_audio, orig_sr=sr, target_sr=16000)
            
            test_path = os.path.join(temp_dir, "test_first_blood.wav")
            sf.write(test_path, test_audio_16k, 16000)
            
            print("🎤 Running detection...")
            events = detector.detect_events(test_path)
            
            print(f"📊 Events detected: {len(events)}")
            
            for event in events:
                print(f"  ✅ {event.event_type.value} at {event.timestamp:.1f}s (confidence: {event.confidence:.3f})")
                if hasattr(event, 'metadata'):
                    print(f"     Metadata: {event.metadata}")
            
            # Check if we detected first_blood
            first_blood_detected = any(event.event_type.value == "first_blood" for event in events)
            
            if first_blood_detected:
                print("🎯 SUCCESS: first_blood detected!")
                return True
            else:
                print("❌ FAILED: first_blood not detected")
                return False
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function."""
    success = test_whisper_simple()
    if success:
        print("\n🎉 Simple Whisper test passed!")
    else:
        print("\n❌ Simple Whisper test failed!")
    return success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
