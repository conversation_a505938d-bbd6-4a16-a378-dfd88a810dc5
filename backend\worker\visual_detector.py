"""Visual event detection using template matching."""

import os
import cv2
import numpy as np
from typing import List, Dict, Tuple, Optional
import structlog

from ..shared.models import DetectedEvent, EventType, ProcessingConfig

logger = structlog.get_logger()


class VisualEventDetector:
    """Detects game events from video frames using template matching."""
    
    def __init__(self, assets_path: str = "assets", config: Optional[ProcessingConfig] = None):
        self.assets_path = assets_path
        self.config = config or ProcessingConfig()
        self.templates = {}
        self.template_scales = [0.8, 0.9, 1.0, 1.1, 1.2]  # Multi-scale matching
        
        # Load template images
        self._load_templates()
    
    def _load_templates(self):
        """Load template images for each event type."""
        templates_dir = os.path.join(self.assets_path, "templates")
        
        # Expected template files for each event type
        template_files = {
            EventType.DOUBLE_KILL: "double_kill_feed.png",
            EventType.TRIPLE_KILL: "triple_kill_feed.png",
            EventType.MANIAC: "maniac_feed.png",
            EventType.SAVAGE: "savage_feed.png",
            EventType.FIRST_BLOOD: "first_blood_feed.png",
            EventType.LORD_KILL: "lord_banner.png",
            EventType.TURTLE_KILL: "turtle_banner.png",
            EventType.CRYSTAL_DESTROY: "crystal_burst.png",
            EventType.TOWER_DESTROY: "tower_destroy.png",
        }
        
        for event_type, filename in template_files.items():
            file_path = os.path.join(templates_dir, filename)
            if os.path.exists(file_path):
                try:
                    # Load template image
                    template = cv2.imread(file_path, cv2.IMREAD_COLOR)
                    if template is not None:
                        # Convert to grayscale for matching
                        template_gray = cv2.cvtColor(template, cv2.COLOR_BGR2GRAY)
                        
                        self.templates[event_type] = {
                            'color': template,
                            'gray': template_gray,
                            'height': template.shape[0],
                            'width': template.shape[1]
                        }
                        
                        logger.info(
                            "Loaded template image",
                            event_type=event_type.value,
                            file_path=file_path,
                            size=(template.shape[1], template.shape[0])
                        )
                    else:
                        logger.error(
                            "Failed to load template image",
                            event_type=event_type.value,
                            file_path=file_path
                        )
                        
                except Exception as e:
                    logger.error(
                        "Error loading template",
                        event_type=event_type.value,
                        file_path=file_path,
                        error=str(e)
                    )
            else:
                logger.warning(
                    "Template file not found",
                    event_type=event_type.value,
                    file_path=file_path
                )
        
        logger.info(
            "Loaded template images",
            count=len(self.templates),
            event_types=[et.value for et in self.templates.keys()]
        )
    
    def extract_frames(self, video_path: str, sample_rate: float = None) -> List[Tuple[float, np.ndarray]]:
        """Extract frames from video at specified sample rate."""
        if sample_rate is None:
            sample_rate = self.config.visual_sample_rate
        
        frames = []
        
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error("Failed to open video", video_path=video_path)
                return frames
            
            # Get video properties
            fps = cap.get(cv2.CAP_PROP_FPS)
            total_frames = int(cap.get(cv2.CAP_PROP_FRAME_COUNT))
            duration = total_frames / fps if fps > 0 else 0
            
            logger.info(
                "Extracting frames from video",
                video_path=video_path,
                fps=fps,
                total_frames=total_frames,
                duration=duration,
                sample_rate=sample_rate
            )
            
            # Calculate frame interval
            frame_interval = int(fps / sample_rate) if fps > 0 else 1
            
            frame_count = 0
            while True:
                ret, frame = cap.read()
                if not ret:
                    break
                
                # Sample frames at specified rate
                if frame_count % frame_interval == 0:
                    timestamp = frame_count / fps if fps > 0 else frame_count
                    frames.append((timestamp, frame))
                
                frame_count += 1
            
            cap.release()
            
            logger.info(
                "Frame extraction complete",
                video_path=video_path,
                extracted_frames=len(frames),
                total_frames=frame_count
            )
            
        except Exception as e:
            logger.error(
                "Frame extraction failed",
                video_path=video_path,
                error=str(e)
            )
        
        return frames
    
    def detect_events(self, video_path: str) -> List[DetectedEvent]:
        """Detect events in video using template matching."""
        if not self.templates:
            logger.warning("No template images loaded")
            return []
        
        detected_events = []
        
        try:
            # Extract frames
            frames = self.extract_frames(video_path)
            if not frames:
                logger.warning("No frames extracted from video")
                return []
            
            logger.info(
                "Starting visual event detection",
                video_path=video_path,
                frames_count=len(frames),
                templates_count=len(self.templates)
            )
            
            # Process each frame
            for timestamp, frame in frames:
                frame_gray = cv2.cvtColor(frame, cv2.COLOR_BGR2GRAY)
                
                # Check each template
                for event_type, template_data in self.templates.items():
                    template_gray = template_data['gray']
                    
                    # Multi-scale template matching
                    best_match = None
                    best_confidence = 0
                    
                    for scale in self.template_scales:
                        # Resize template
                        scaled_template = cv2.resize(
                            template_gray,
                            None,
                            fx=scale,
                            fy=scale,
                            interpolation=cv2.INTER_CUBIC
                        )
                        
                        # Skip if template is larger than frame
                        if (scaled_template.shape[0] > frame_gray.shape[0] or 
                            scaled_template.shape[1] > frame_gray.shape[1]):
                            continue
                        
                        # Perform template matching
                        result = cv2.matchTemplate(
                            frame_gray,
                            scaled_template,
                            cv2.TM_CCOEFF_NORMED
                        )
                        
                        # Find best match
                        min_val, max_val, min_loc, max_loc = cv2.minMaxLoc(result)
                        
                        if max_val > best_confidence:
                            best_confidence = max_val
                            best_match = {
                                'location': max_loc,
                                'scale': scale,
                                'template_size': scaled_template.shape
                            }
                    
                    # Check if confidence exceeds threshold
                    if best_confidence > self.config.visual_similarity_threshold:
                        detected_events.append(DetectedEvent(
                            event_type=event_type,
                            timestamp=timestamp,
                            confidence=float(best_confidence),
                            source="visual",
                            metadata={
                                'match_location': best_match['location'],
                                'template_scale': best_match['scale'],
                                'template_size': best_match['template_size'],
                                'ncc_score': best_confidence
                            }
                        ))
                        
                        logger.info(
                            "Detected visual event",
                            event_type=event_type.value,
                            timestamp=timestamp,
                            confidence=best_confidence,
                            location=best_match['location']
                        )
            
            # Merge nearby events
            detected_events = self._merge_nearby_events(detected_events)
            
            logger.info(
                "Visual event detection complete",
                video_path=video_path,
                total_events=len(detected_events),
                event_types=[e.event_type.value for e in detected_events]
            )
            
        except Exception as e:
            logger.error(
                "Visual event detection failed",
                video_path=video_path,
                error=str(e)
            )
        
        return detected_events
    
    def _merge_nearby_events(self, events: List[DetectedEvent]) -> List[DetectedEvent]:
        """Merge events of the same type that are close in time."""
        if not events:
            return events
        
        # Sort by timestamp
        events.sort(key=lambda e: e.timestamp)
        
        merged_events = []
        current_event = events[0]
        
        for next_event in events[1:]:
            # Check if same event type and within merge window
            if (next_event.event_type == current_event.event_type and
                next_event.timestamp - current_event.timestamp <= self.config.merge_window):
                
                # Keep the one with higher confidence
                if next_event.confidence > current_event.confidence:
                    current_event = next_event
            else:
                # Different event or outside merge window
                merged_events.append(current_event)
                current_event = next_event
        
        # Add the last event
        merged_events.append(current_event)
        
        logger.info(
            "Merged nearby visual events",
            original_count=len(events),
            merged_count=len(merged_events)
        )
        
        return merged_events
    
    def create_template(self, video_path: str, timestamp: float, event_type: EventType, 
                       bbox: Tuple[int, int, int, int], output_path: str):
        """Create a template from a video frame (utility function)."""
        try:
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.error("Failed to open video for template creation", video_path=video_path)
                return
            
            # Seek to timestamp
            fps = cap.get(cv2.CAP_PROP_FPS)
            frame_number = int(timestamp * fps)
            cap.set(cv2.CAP_PROP_POS_FRAMES, frame_number)
            
            ret, frame = cap.read()
            if ret:
                # Extract region of interest
                x, y, w, h = bbox
                template = frame[y:y+h, x:x+w]
                
                # Save template
                cv2.imwrite(output_path, template)
                
                logger.info(
                    "Created template",
                    event_type=event_type.value,
                    video_path=video_path,
                    timestamp=timestamp,
                    bbox=bbox,
                    output_path=output_path
                )
            else:
                logger.error("Failed to read frame for template creation")
            
            cap.release()
            
        except Exception as e:
            logger.error(
                "Template creation failed",
                event_type=event_type.value,
                error=str(e)
            )
