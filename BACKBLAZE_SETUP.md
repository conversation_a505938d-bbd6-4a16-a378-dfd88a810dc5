# Backblaze B2 Setup Guide

This guide will help you configure ML Highlights to use Backblaze B2 instead of AWS S3 for storage.

## Why Backblaze B2?

- **Cost-effective**: Much cheaper than AWS S3 for storage and bandwidth
- **S3-compatible**: Works with existing S3 tools and libraries
- **Simple pricing**: No complex pricing tiers or hidden fees
- **Reliable**: Enterprise-grade cloud storage

## Step 1: Create Backblaze B2 Account

1. Go to [backblaze.com](https://www.backblaze.com/b2/cloud-storage.html)
2. Sign up for a B2 account
3. Verify your email and complete account setup

## Step 2: Create Application Key

1. Log into your Backblaze B2 account
2. Go to **App Keys** in the left sidebar
3. Click **Add a New Application Key**
4. Configure the key:
   - **Key Name**: `ml-highlights-app`
   - **Allow access to Bucket(s)**: Select your bucket or "All"
   - **Type of Access**: Read and Write
   - **Allow List All Bucket Names**: ✅ (checked)
5. Click **Create New Key**
6. **IMPORTANT**: Copy both the `keyID` and `applicationKey` immediately (you won't see the applicationKey again)

## Step 3: Create B2 Bucket

1. In your Backblaze B2 dashboard, go to **Buckets**
2. Click **Create a Bucket**
3. Configure the bucket:
   - **Bucket Name**: `ml-highlights-storage` (or your preferred name)
   - **Files in Bucket are**: Private (recommended)
   - **Default Encryption**: Disabled (optional)
4. Click **Create a Bucket**
5. Note your bucket name for configuration

## Step 4: Find Your Endpoint URL

1. In your bucket details, look for the **Endpoint** information
2. The format will be: `https://s3.{region}.backblazeb2.com`
3. Common regions:
   - `us-west-004` (US West)
   - `us-west-002` (US West)
   - `eu-central-003` (EU Central)

## Step 5: Configure ML Highlights

Create or update your `.env` file with your Backblaze B2 credentials:

```bash
# Backblaze B2 Configuration
STORAGE_PROVIDER=backblaze
S3_BUCKET=ml-highlights-storage
S3_ACCESS_KEY_ID=K005w4ikLOftDCqa3/SuRqQcTdzV6Uk
S3_SECRET_ACCESS_KEY=your_application_key_here
S3_REGION=us-west-004
S3_ENDPOINT_URL=https://s3.us-west-004.backblazeb2.com

# Redis Configuration
REDIS_URL=redis://localhost:6379

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
DEBUG=true
```

**Replace the following values:**
- `S3_BUCKET`: Your actual bucket name
- `S3_SECRET_ACCESS_KEY`: Your Backblaze applicationKey
- `S3_REGION`: Your bucket's region
- `S3_ENDPOINT_URL`: Your bucket's endpoint URL

## Step 6: Test Configuration

1. Start the services:
   ```bash
   python start_services.py
   ```

2. Check the logs for successful storage initialization:
   ```
   ✅ Initialized storage client
      provider: backblaze
      bucket: ml-highlights-storage
      endpoint: https://s3.us-west-004.backblazeb2.com
   ```

3. Test with a small upload:
   ```bash
   python test_api.py
   ```

## Pricing Comparison

**Backblaze B2 vs AWS S3** (approximate):

| Feature | Backblaze B2 | AWS S3 Standard |
|---------|--------------|-----------------|
| Storage | $0.005/GB/month | $0.023/GB/month |
| Download | $0.01/GB | $0.09/GB |
| Upload | Free | Free |
| API Calls | First 2,500 free daily | $0.0004 per 1,000 |

**Example**: 100GB storage + 50GB downloads/month
- **Backblaze B2**: ~$1.00/month
- **AWS S3**: ~$6.80/month

## Troubleshooting

### Common Issues

1. **Authentication Error**
   ```
   Error: InvalidAccessKeyId
   ```
   - Check your `S3_ACCESS_KEY_ID` (keyID)
   - Verify your `S3_SECRET_ACCESS_KEY` (applicationKey)

2. **Bucket Not Found**
   ```
   Error: NoSuchBucket
   ```
   - Verify your bucket name in `S3_BUCKET`
   - Check that the bucket exists in your Backblaze account

3. **Wrong Endpoint**
   ```
   Error: Could not connect to the endpoint URL
   ```
   - Verify your `S3_ENDPOINT_URL`
   - Check your bucket's region in `S3_REGION`

4. **Permission Denied**
   ```
   Error: AccessDenied
   ```
   - Check your application key permissions
   - Ensure the key has Read/Write access to the bucket

### Testing Connection

You can test your Backblaze B2 connection with this Python script:

```python
import boto3
from botocore.exceptions import ClientError

# Your configuration
ACCESS_KEY = "K005w4ikLOftDCqa3/SuRqQcTdzV6Uk"
SECRET_KEY = "your_application_key"
ENDPOINT = "https://s3.us-west-004.backblazeb2.com"
BUCKET = "ml-highlights-storage"

# Test connection
try:
    s3 = boto3.client(
        's3',
        aws_access_key_id=ACCESS_KEY,
        aws_secret_access_key=SECRET_KEY,
        endpoint_url=ENDPOINT
    )
    
    # List buckets
    response = s3.list_buckets()
    print("✅ Connection successful!")
    print("Buckets:", [b['Name'] for b in response['Buckets']])
    
except ClientError as e:
    print(f"❌ Connection failed: {e}")
```

## Security Notes

- **Never commit your application key to version control**
- Use environment variables or secure secret management
- Rotate your application keys periodically
- Use bucket-specific keys when possible (not account-wide keys)
- Enable bucket versioning for important data

## Support

- **Backblaze B2 Documentation**: https://www.backblaze.com/b2/docs/
- **S3 Compatibility**: https://www.backblaze.com/b2/docs/s3_compatible_api.html
- **ML Highlights Issues**: Create an issue in the GitHub repository
