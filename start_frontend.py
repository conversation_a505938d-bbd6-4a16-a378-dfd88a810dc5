#!/usr/bin/env python3
"""
Start the Streamlit frontend for ML Highlights.
"""

import subprocess
import sys
import os
from pathlib import Path

def check_streamlit():
    """Check if Streamlit is installed."""
    try:
        import streamlit
        return True
    except ImportError:
        return False

def install_frontend_deps():
    """Install frontend dependencies."""
    print("📦 Installing frontend dependencies...")
    
    frontend_dir = Path("frontend")
    requirements_file = frontend_dir / "requirements.txt"
    
    if requirements_file.exists():
        cmd = [sys.executable, "-m", "pip", "install", "-r", str(requirements_file)]
        result = subprocess.run(cmd, capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Frontend dependencies installed successfully!")
            return True
        else:
            print(f"❌ Failed to install dependencies: {result.stderr}")
            return False
    else:
        print(f"❌ Requirements file not found: {requirements_file}")
        return False

def start_streamlit():
    """Start the Streamlit application."""
    frontend_dir = Path("frontend")
    app_file = frontend_dir / "streamlit_app.py"
    
    if not app_file.exists():
        print(f"❌ Streamlit app not found: {app_file}")
        return False
    
    print("🚀 Starting ML Highlights Frontend...")
    print("📱 Frontend will be available at: http://localhost:8501")
    print("🔗 Make sure the backend API is running at: http://localhost:8000")
    print("\n" + "="*60)
    
    # Start Streamlit
    cmd = [
        sys.executable, "-m", "streamlit", "run", 
        str(app_file),
        "--server.port", "8501",
        "--server.address", "localhost",
        "--browser.gatherUsageStats", "false"
    ]
    
    try:
        subprocess.run(cmd)
        return True
    except KeyboardInterrupt:
        print("\n👋 Frontend stopped by user")
        return True
    except Exception as e:
        print(f"❌ Failed to start frontend: {e}")
        return False

def main():
    """Main function."""
    print("🎮 ML Highlights Frontend Starter")
    print("=" * 50)
    
    # Check if Streamlit is installed
    if not check_streamlit():
        print("📦 Streamlit not found. Installing dependencies...")
        if not install_frontend_deps():
            print("❌ Failed to install dependencies. Exiting.")
            return 1
    
    # Start the frontend
    if start_streamlit():
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
